#!/usr/bin/env python3
"""
نسخة محدثة من نظام إدارة المخازن
تحل مشكلة TypeError نهائياً
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import sqlite3
import os
import sys

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'warehouse-secret-key-2025'

# مسار قاعدة البيانات
DATABASE = 'warehouse_fixed.db'

def safe_float(value, default=0.0):
    """تحويل آمن للقيم إلى float"""
    if value is None:
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """تحويل آمن للقيم إلى int"""
    if value is None:
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_str(value, default=""):
    """تحويل آمن للقيم إلى string"""
    if value is None:
        return default
    try:
        return str(value)
    except:
        return default

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """تهيئة قاعدة البيانات"""
    conn = get_db_connection()
    
    # إنشاء جدول المستخدمين
    conn.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            full_name TEXT NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول أنواع الخشب
    conn.execute('''
        CREATE TABLE IF NOT EXISTS wood_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_ar TEXT NOT NULL,
            name_fr TEXT,
            description TEXT,
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول المنتجات
    conn.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT UNIQUE NOT NULL,
            wood_type_id INTEGER,
            length REAL DEFAULT 0,
            width REAL DEFAULT 0,
            thickness REAL DEFAULT 0,
            quantity REAL DEFAULT 0,
            unit TEXT DEFAULT 'قطعة',
            cost_price REAL DEFAULT 0,
            selling_price REAL DEFAULT 0,
            min_quantity REAL DEFAULT 0,
            quality_grade TEXT DEFAULT '',
            location TEXT DEFAULT '',
            notes TEXT DEFAULT '',
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (wood_type_id) REFERENCES wood_types (id)
        )
    ''')
    
    # إنشاء جدول العملاء
    conn.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contact_person TEXT DEFAULT '',
            phone TEXT DEFAULT '',
            email TEXT DEFAULT '',
            address TEXT DEFAULT '',
            balance REAL DEFAULT 0,
            credit_limit REAL DEFAULT 0,
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء المستخدم الافتراضي
    admin_exists = conn.execute('SELECT id FROM users WHERE username = ?', ('admin',)).fetchone()
    if not admin_exists:
        conn.execute('''
            INSERT INTO users (username, email, full_name, password_hash, role)
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', 'المدير العام', 
              generate_password_hash('admin123'), 'admin'))
    
    # إضافة أنواع خشب افتراضية
    wood_types_data = [
        ('خشب زان', 'Bois de hêtre', 'خشب صلب عالي الجودة'),
        ('خشب سنديان', 'Bois de chêne', 'خشب قوي ومتين'),
        ('خشب صنوبر', 'Bois de pin', 'خشب لين سهل التشكيل'),
        ('MDF', 'MDF', 'خشب مضغوط متوسط الكثافة')
    ]
    
    for name_ar, name_fr, description in wood_types_data:
        exists = conn.execute('SELECT id FROM wood_types WHERE name_ar = ?', (name_ar,)).fetchone()
        if not exists:
            conn.execute('''
                INSERT INTO wood_types (name_ar, name_fr, description)
                VALUES (?, ?, ?)
            ''', (name_ar, name_fr, description))
    
    # إضافة منتجات تجريبية
    sample_products = [
        ('لوح خشب زان 2×4', 'PRD-BEECH-001', 1, 2.0, 4.0, 0.05, 100.0, 'قطعة', 25.0, 35.0, 20.0, 'ممتاز', 'المخزن الرئيسي - رف A1'),
        ('لوح MDF 2×3', 'PRD-MDF-001', 4, 2.0, 3.0, 0.018, 15.0, 'قطعة', 15.0, 22.0, 50.0, 'تجاري', 'المخزن الثانوي - رف C1'),
        ('خشب صنوبر 3×2', 'PRD-PINE-001', 3, 3.0, 2.0, 0.025, 75.0, 'قطعة', 18.0, 28.0, 30.0, 'جيد', 'المخزن الرئيسي - رف B2')
    ]
    
    for product_data in sample_products:
        exists = conn.execute('SELECT id FROM products WHERE code = ?', (product_data[1],)).fetchone()
        if not exists:
            conn.execute('''
                INSERT INTO products (name, code, wood_type_id, length, width, thickness, 
                                    quantity, unit, cost_price, selling_price, min_quantity, 
                                    quality_grade, location)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', product_data)
    
    # إضافة عملاء تجريبيين
    sample_customers = [
        ('شركة الأثاث الحديث', 'أحمد محمد', '0501234567', '<EMAIL>', 'الرياض، حي الملز', 0.0, 50000.0),
        ('مصنع الخشب الذهبي', 'فاطمة أحمد', '0509876543', '<EMAIL>', 'جدة، حي الفيصلية', 1500.0, 30000.0)
    ]
    
    for customer_data in sample_customers:
        exists = conn.execute('SELECT id FROM customers WHERE name = ?', (customer_data[0],)).fetchone()
        if not exists:
            conn.execute('''
                INSERT INTO customers (name, contact_person, phone, email, address, balance, credit_limit)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', customer_data)
    
    conn.commit()
    conn.close()

def process_product_data(product_raw):
    """معالجة بيانات المنتج وتحويلها إلى أنواع آمنة"""
    product = dict(product_raw)
    
    # تحويل القيم الرقمية بأمان
    product['quantity'] = safe_float(product.get('quantity', 0))
    product['min_quantity'] = safe_float(product.get('min_quantity', 0))
    product['cost_price'] = safe_float(product.get('cost_price', 0))
    product['selling_price'] = safe_float(product.get('selling_price', 0))
    product['length'] = safe_float(product.get('length', 0))
    product['width'] = safe_float(product.get('width', 0))
    product['thickness'] = safe_float(product.get('thickness', 0))
    
    # تحويل القيم النصية
    product['name'] = safe_str(product.get('name', ''))
    product['code'] = safe_str(product.get('code', ''))
    product['unit'] = safe_str(product.get('unit', 'قطعة'))
    product['quality_grade'] = safe_str(product.get('quality_grade', ''))
    product['location'] = safe_str(product.get('location', ''))
    product['wood_type_name'] = safe_str(product.get('wood_type_name', 'غير محدد'))
    
    # حساب حالة المخزون
    if product['quantity'] <= product['min_quantity']:
        product['stock_status'] = 'low'
        product['stock_badge'] = 'danger'
        product['stock_text'] = 'نقص مخزون'
        product['stock_icon'] = 'exclamation-triangle'
    elif product['quantity'] <= (product['min_quantity'] * 1.5):
        product['stock_status'] = 'warning'
        product['stock_badge'] = 'warning'
        product['stock_text'] = 'تحذير'
        product['stock_icon'] = 'exclamation-circle'
    else:
        product['stock_status'] = 'good'
        product['stock_badge'] = 'success'
        product['stock_text'] = 'متوفر'
        product['stock_icon'] = 'check'
    
    return product

def process_customer_data(customer_raw):
    """معالجة بيانات العميل وتحويلها إلى أنواع آمنة"""
    customer = dict(customer_raw)
    
    # تحويل القيم الرقمية بأمان
    customer['balance'] = safe_float(customer.get('balance', 0))
    customer['credit_limit'] = safe_float(customer.get('credit_limit', 0))
    
    # تحويل القيم النصية
    customer['name'] = safe_str(customer.get('name', ''))
    customer['contact_person'] = safe_str(customer.get('contact_person', ''))
    customer['phone'] = safe_str(customer.get('phone', ''))
    customer['email'] = safe_str(customer.get('email', ''))
    customer['address'] = safe_str(customer.get('address', ''))
    
    # حساب حالة الرصيد
    if customer['balance'] > 0:
        customer['balance_status'] = 'debt'
        customer['balance_class'] = 'text-danger'
        customer['balance_text'] = f"{customer['balance']:.2f} ر.س مدين"
    elif customer['balance'] < 0:
        customer['balance_status'] = 'credit'
        customer['balance_class'] = 'text-success'
        customer['balance_text'] = f"{abs(customer['balance']):.2f} ر.س دائن"
    else:
        customer['balance_status'] = 'zero'
        customer['balance_class'] = 'text-muted'
        customer['balance_text'] = "0.00 ر.س"
    
    return customer

# الصفحة الرئيسية
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    
    try:
        # إحصائيات بسيطة
        total_products = conn.execute('SELECT COUNT(*) FROM products WHERE is_active = 1').fetchone()[0]
        active_customers = conn.execute('SELECT COUNT(*) FROM customers WHERE is_active = 1').fetchone()[0]
        
        # المنتجات التي تحتاج إعادة تموين
        products_raw = conn.execute('''
            SELECT * FROM products WHERE is_active = 1 LIMIT 10
        ''').fetchall()
        
        low_stock_products = []
        for product_raw in products_raw:
            product = process_product_data(product_raw)
            if product['stock_status'] == 'low':
                low_stock_products.append(product)
            if len(low_stock_products) >= 5:
                break
        
    except Exception as e:
        print(f"خطأ في جلب البيانات: {e}")
        total_products = 0
        active_customers = 0
        low_stock_products = []
    finally:
        conn.close()
    
    return render_template('dashboard_fixed.html',
                         total_products=total_products,
                         today_sales=0,
                         active_customers=active_customers,
                         low_stock_alerts=len(low_stock_products),
                         low_stock_products=low_stock_products,
                         recent_sales=[])

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        try:
            user_data = conn.execute('''
                SELECT * FROM users WHERE username = ? AND is_active = 1
            ''', (username,)).fetchone()
            
            if user_data and check_password_hash(user_data['password_hash'], password):
                session['user_id'] = user_data['id']
                session['username'] = user_data['username']
                session['full_name'] = user_data['full_name']
                session['role'] = user_data['role']
                flash('تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('index'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            flash('حدث خطأ في تسجيل الدخول', 'error')
        finally:
            conn.close()
    
    return render_template('login_basic.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

# صفحة المنتجات
@app.route('/products')
def products():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    try:
        products_raw = conn.execute('''
            SELECT p.*, w.name_ar as wood_type_name 
            FROM products p
            LEFT JOIN wood_types w ON p.wood_type_id = w.id
            WHERE p.is_active = 1
            ORDER BY p.created_at DESC
        ''').fetchall()
        
        # معالجة البيانات
        products = [process_product_data(product) for product in products_raw]
        
    except Exception as e:
        print(f"خطأ في جلب المنتجات: {e}")
        products = []
    finally:
        conn.close()
    
    return render_template('products_fixed.html', products=products)

# صفحة العملاء
@app.route('/customers')
def customers():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    try:
        customers_raw = conn.execute('''
            SELECT * FROM customers WHERE is_active = 1 ORDER BY created_at DESC
        ''').fetchall()
        
        # معالجة البيانات
        customers = [process_customer_data(customer) for customer in customers_raw]
        
    except Exception as e:
        print(f"خطأ في جلب العملاء: {e}")
        customers = []
    finally:
        conn.close()
    
    return render_template('customers_fixed.html', customers=customers)

# API للحصول على معلومات النظام
@app.route('/api/system_info')
def system_info():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    
    conn = get_db_connection()
    try:
        info = {
            'total_products': conn.execute('SELECT COUNT(*) FROM products WHERE is_active = 1').fetchone()[0],
            'total_customers': conn.execute('SELECT COUNT(*) FROM customers WHERE is_active = 1').fetchone()[0],
            'total_wood_types': conn.execute('SELECT COUNT(*) FROM wood_types WHERE is_active = 1').fetchone()[0],
            'low_stock_count': 0
        }
        
        # حساب المنتجات التي تحتاج إعادة تموين
        products_raw = conn.execute('SELECT quantity, min_quantity FROM products WHERE is_active = 1').fetchall()
        low_stock_count = 0
        for product in products_raw:
            qty = safe_float(product['quantity'])
            min_qty = safe_float(product['min_quantity'])
            if qty <= min_qty:
                low_stock_count += 1
        
        info['low_stock_count'] = low_stock_count
        
    except Exception as e:
        print(f"خطأ في API: {e}")
        info = {
            'total_products': 0,
            'total_customers': 0,
            'total_wood_types': 0,
            'low_stock_count': 0
        }
    finally:
        conn.close()
    
    return jsonify(info)

if __name__ == '__main__':
    # تهيئة قاعدة البيانات
    init_database()
    
    print("🚀 تم تشغيل نظام إدارة المخازن (النسخة المحدثة)")
    print("📍 الرابط: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("⏹️ لإيقاف النظام اضغط Ctrl+C")
    print("")
    print("✅ تم حل مشكلة TypeError نهائياً!")
    print("")
    
    # إعدادات للتطوير المحلي
    app.config['ENV'] = 'development'
    app.run(debug=True, host='0.0.0.0', port=5000)
