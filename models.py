from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime

# نموذج المستخدمين
class User(UserMixin):
    def __init__(self, db):
        self.db = db
        
    id = None
    username = None
    email = None
    full_name = None
    password_hash = None
    role = 'user'
    is_active = True
    created_at = None
    
    def __repr__(self):
        return f'<User {self.username}>'

# دالة لإنشاء النماذج
def create_models(db):
    
    # نموذج المستخدمين
    class User(UserMixin, db.Model):
        __tablename__ = 'user'
        
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        full_name = db.Column(db.String(200), nullable=False)
        password_hash = db.Column(db.String(200), nullable=False)
        role = db.Column(db.String(50), nullable=False, default='user')
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        def __repr__(self):
            return f'<User {self.username}>'

    # نموذج أنواع الخشب
    class WoodType(db.Model):
        __tablename__ = 'wood_type'
        
        id = db.Column(db.Integer, primary_key=True)
        name_ar = db.Column(db.String(100), nullable=False)
        name_fr = db.Column(db.String(100), nullable=True)
        description = db.Column(db.Text)
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        products = db.relationship('Product', backref='wood_type', lazy=True)

    # نموذج المنتجات
    class Product(db.Model):
        __tablename__ = 'product'
        
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        code = db.Column(db.String(50), unique=True, nullable=False)
        wood_type_id = db.Column(db.Integer, db.ForeignKey('wood_type.id'), nullable=False)
        
        length = db.Column(db.Float)
        width = db.Column(db.Float)
        thickness = db.Column(db.Float)
        
        quantity = db.Column(db.Float, default=0)
        unit = db.Column(db.String(20), default='متر مكعب')
        
        cost_price = db.Column(db.Float, default=0)
        selling_price = db.Column(db.Float, default=0)
        min_quantity = db.Column(db.Float, default=0)
        
        quality_grade = db.Column(db.String(50))
        supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'))
        location = db.Column(db.String(100))
        notes = db.Column(db.Text)
        
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        
        def __repr__(self):
            return f'<Product {self.name}>'

    # نموذج الموردين
    class Supplier(db.Model):
        __tablename__ = 'supplier'
        
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        contact_person = db.Column(db.String(100))
        phone = db.Column(db.String(20))
        email = db.Column(db.String(120))
        address = db.Column(db.Text)
        tax_number = db.Column(db.String(50))
        
        balance = db.Column(db.Float, default=0)
        credit_limit = db.Column(db.Float, default=0)
        
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        products = db.relationship('Product', backref='supplier', lazy=True)
        purchase_invoices = db.relationship('PurchaseInvoice', backref='supplier', lazy=True)

    # نموذج العملاء
    class Customer(db.Model):
        __tablename__ = 'customer'
        
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(200), nullable=False)
        contact_person = db.Column(db.String(100))
        phone = db.Column(db.String(20))
        email = db.Column(db.String(120))
        address = db.Column(db.Text)
        tax_number = db.Column(db.String(50))
        
        balance = db.Column(db.Float, default=0)
        credit_limit = db.Column(db.Float, default=0)
        
        is_active = db.Column(db.Boolean, default=True)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        sales_invoices = db.relationship('SalesInvoice', backref='customer', lazy=True)

    # نموذج فواتير المبيعات
    class SalesInvoice(db.Model):
        __tablename__ = 'sales_invoice'
        
        id = db.Column(db.Integer, primary_key=True)
        invoice_number = db.Column(db.String(50), unique=True, nullable=False)
        customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
        
        invoice_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
        due_date = db.Column(db.Date)
        
        subtotal = db.Column(db.Float, default=0)
        tax_rate = db.Column(db.Float, default=0)
        tax_amount = db.Column(db.Float, default=0)
        discount_rate = db.Column(db.Float, default=0)
        discount_amount = db.Column(db.Float, default=0)
        total_amount = db.Column(db.Float, default=0)
        paid_amount = db.Column(db.Float, default=0)
        
        status = db.Column(db.String(20), default='draft')
        payment_status = db.Column(db.String(20), default='unpaid')
        
        notes = db.Column(db.Text)
        created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        items = db.relationship('SalesInvoiceItem', backref='invoice', lazy=True, cascade='all, delete-orphan')

    # نموذج عناصر فاتورة المبيعات
    class SalesInvoiceItem(db.Model):
        __tablename__ = 'sales_invoice_item'
        
        id = db.Column(db.Integer, primary_key=True)
        invoice_id = db.Column(db.Integer, db.ForeignKey('sales_invoice.id'), nullable=False)
        product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
        
        quantity = db.Column(db.Float, nullable=False)
        unit_price = db.Column(db.Float, nullable=False)
        total_price = db.Column(db.Float, nullable=False)
        
        product = db.relationship('Product', backref='sales_items')

    # نموذج فواتير المشتريات
    class PurchaseInvoice(db.Model):
        __tablename__ = 'purchase_invoice'
        
        id = db.Column(db.Integer, primary_key=True)
        invoice_number = db.Column(db.String(50), unique=True, nullable=False)
        supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'), nullable=False)
        
        invoice_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
        due_date = db.Column(db.Date)
        
        subtotal = db.Column(db.Float, default=0)
        tax_rate = db.Column(db.Float, default=0)
        tax_amount = db.Column(db.Float, default=0)
        discount_rate = db.Column(db.Float, default=0)
        discount_amount = db.Column(db.Float, default=0)
        total_amount = db.Column(db.Float, default=0)
        paid_amount = db.Column(db.Float, default=0)
        
        status = db.Column(db.String(20), default='draft')
        payment_status = db.Column(db.String(20), default='unpaid')
        
        notes = db.Column(db.Text)
        created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        
        items = db.relationship('PurchaseInvoiceItem', backref='invoice', lazy=True, cascade='all, delete-orphan')

    # نموذج عناصر فاتورة المشتريات
    class PurchaseInvoiceItem(db.Model):
        __tablename__ = 'purchase_invoice_item'
        
        id = db.Column(db.Integer, primary_key=True)
        invoice_id = db.Column(db.Integer, db.ForeignKey('purchase_invoice.id'), nullable=False)
        product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
        
        quantity = db.Column(db.Float, nullable=False)
        unit_price = db.Column(db.Float, nullable=False)
        total_price = db.Column(db.Float, nullable=False)
        
        product = db.relationship('Product', backref='purchase_items')

    # نموذج المدفوعات
    class Payment(db.Model):
        __tablename__ = 'payment'
        
        id = db.Column(db.Integer, primary_key=True)
        payment_type = db.Column(db.String(20), nullable=False)
        invoice_id = db.Column(db.Integer, nullable=False)
        amount = db.Column(db.Float, nullable=False)
        payment_method = db.Column(db.String(50), default='cash')
        payment_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
        reference_number = db.Column(db.String(100))
        notes = db.Column(db.Text)
        created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
        created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # نموذج إعدادات النظام
    class SystemSettings(db.Model):
        __tablename__ = 'system_settings'
        
        id = db.Column(db.Integer, primary_key=True)
        key = db.Column(db.String(100), unique=True, nullable=False)
        value = db.Column(db.Text)
        description = db.Column(db.Text)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    return {
        'User': User,
        'WoodType': WoodType,
        'Product': Product,
        'Supplier': Supplier,
        'Customer': Customer,
        'SalesInvoice': SalesInvoice,
        'SalesInvoiceItem': SalesInvoiceItem,
        'PurchaseInvoice': PurchaseInvoice,
        'PurchaseInvoiceItem': PurchaseInvoiceItem,
        'Payment': Payment,
        'SystemSettings': SystemSettings
    }
