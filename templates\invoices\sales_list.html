{% extends "base.html" %}

{% block title %}فواتير المبيعات - نظام إدارة المخازن{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-invoice me-2"></i>
        فواتير المبيعات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('add_sales_invoice') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                فاتورة مبيعات جديدة
            </a>
        </div>
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-filter"></i>
                تصفية
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="?status=draft">مسودة</a></li>
                <li><a class="dropdown-item" href="?status=confirmed">مؤكدة</a></li>
                <li><a class="dropdown-item" href="?status=paid">مدفوعة</a></li>
                <li><a class="dropdown-item" href="?status=cancelled">ملغية</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="{{ url_for('sales_invoices') }}">جميع الفواتير</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- إحصائيات الفواتير -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ invoices|length }}</h4>
                        <p class="mb-0">إجمالي الفواتير</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "%.2f"|format(invoices|selectattr("status", "equalto", "confirmed")|sum(attribute="total_amount")) }}</h4>
                        <p class="mb-0">إجمالي المبيعات (ر.س)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ invoices|selectattr("payment_status", "equalto", "unpaid")|list|length }}</h4>
                        <p class="mb-0">فواتير غير مدفوعة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ invoices|selectattr("status", "equalto", "draft")|list|length }}</h4>
                        <p class="mb-0">مسودات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-edit fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الفواتير -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة فواتير المبيعات
        </h5>
    </div>
    <div class="card-body">
        {% if invoices %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>تاريخ الفاتورة</th>
                        <th>المبلغ الإجمالي</th>
                        <th>المبلغ المدفوع</th>
                        <th>المتبقي</th>
                        <th>حالة الفاتورة</th>
                        <th>حالة الدفع</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td>
                            <strong>{{ invoice.invoice_number }}</strong>
                        </td>
                        <td>
                            {% if invoice.customer %}
                                {{ invoice.customer.name }}
                                <br><small class="text-muted">{{ invoice.customer.phone or '' }}</small>
                            {% else %}
                                <span class="text-muted">عميل محذوف</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else 'غير محدد' }}
                            {% if invoice.due_date %}
                            <br><small class="text-muted">استحقاق: {{ invoice.due_date.strftime('%Y-%m-%d') }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ "%.2f"|format(invoice.total_amount) }} ر.س</strong>
                        </td>
                        <td>
                            {{ "%.2f"|format(invoice.paid_amount) }} ر.س
                        </td>
                        <td>
                            {% set remaining = invoice.total_amount - invoice.paid_amount %}
                            {% if remaining > 0 %}
                                <span class="text-danger">{{ "%.2f"|format(remaining) }} ر.س</span>
                            {% else %}
                                <span class="text-success">0.00 ر.س</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.status == 'draft' %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-edit"></i>
                                    مسودة
                                </span>
                            {% elif invoice.status == 'confirmed' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i>
                                    مؤكدة
                                </span>
                            {% elif invoice.status == 'cancelled' %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-times"></i>
                                    ملغية
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.payment_status == 'paid' %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle"></i>
                                    مدفوعة
                                </span>
                            {% elif invoice.payment_status == 'partial' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock"></i>
                                    جزئية
                                </span>
                            {% else %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-exclamation-circle"></i>
                                    غير مدفوعة
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" title="عرض"
                                        onclick="viewInvoice({{ invoice.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if invoice.status == 'draft' %}
                                <button type="button" class="btn btn-outline-success" title="تعديل"
                                        onclick="editInvoice({{ invoice.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% endif %}
                                <button type="button" class="btn btn-outline-info" title="طباعة"
                                        onclick="printInvoice({{ invoice.id }})">
                                    <i class="fas fa-print"></i>
                                </button>
                                {% if invoice.payment_status != 'paid' %}
                                <button type="button" class="btn btn-outline-warning" title="دفعة"
                                        onclick="addPayment({{ invoice.id }})">
                                    <i class="fas fa-money-bill"></i>
                                </button>
                                {% endif %}
                                {% if invoice.status == 'draft' %}
                                <button type="button" class="btn btn-outline-danger" title="حذف"
                                        onclick="deleteInvoice({{ invoice.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد فواتير مبيعات</h5>
            <p class="text-muted">ابدأ بإنشاء فاتورة مبيعات جديدة</p>
            <a href="{{ url_for('add_sales_invoice') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                فاتورة مبيعات جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- معلومات إضافية -->
{% if invoices %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar text-info me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-success">{{ "%.2f"|format(invoices|sum(attribute="paid_amount")) }}</h5>
                        <small class="text-muted">إجمالي المدفوع</small>
                    </div>
                    <div class="col-6">
                        {% set total_remaining = invoices|sum(attribute="total_amount") - invoices|sum(attribute="paid_amount") %}
                        <h5 class="text-danger">{{ "%.2f"|format(total_remaining) }}</h5>
                        <small class="text-muted">إجمالي المتبقي</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    فواتير تحتاج متابعة
                </h6>
            </div>
            <div class="card-body">
                {% set overdue_invoices = invoices|selectattr("due_date")|selectattr("payment_status", "ne", "paid")|list %}
                {% if overdue_invoices %}
                <ul class="list-unstyled mb-0">
                    {% for invoice in overdue_invoices[:3] %}
                    <li class="mb-2">
                        <strong>{{ invoice.invoice_number }}</strong>
                        <br>
                        <small class="text-muted">
                            {{ invoice.customer.name if invoice.customer else 'عميل محذوف' }} - 
                            {{ "%.2f"|format(invoice.total_amount - invoice.paid_amount) }} ر.س
                        </small>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-success mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    جميع الفواتير محدثة
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function viewInvoice(id) {
    window.location.href = '/sales-invoices/' + id;
}

function editInvoice(id) {
    window.location.href = '/sales-invoices/' + id + '/edit';
}

function printInvoice(id) {
    window.open('/sales-invoices/' + id + '/print', '_blank');
}

function addPayment(id) {
    // سيتم تطوير نافذة إضافة دفعة
    alert('سيتم تطوير نافذة إضافة دفعة قريباً - الفاتورة رقم: ' + id);
}

function deleteInvoice(id) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // سيتم تطوير وظيفة الحذف
        alert('سيتم تطوير وظيفة الحذف قريباً - الفاتورة رقم: ' + id);
    }
}

// تحديث الصفحة كل 30 ثانية للحصول على آخر التحديثات
setInterval(function() {
    // يمكن إضافة تحديث تلقائي هنا إذا لزم الأمر
}, 30000);
</script>
{% endblock %}
