{% extends "base.html" %}

{% block title %}إضافة منتج جديد - نظام إدارة المخازن{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus-circle me-2"></i>
        إضافة منتج جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('products') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المنتج الأساسية
                </h5>
            </div>
            <div class="card-body">
                <form id="addProductForm" method="POST" action="/products/add">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       placeholder="مثال: لوح خشب زان 2×4">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label">كود المنتج *</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="code" name="code" required 
                                           placeholder="PRD-XXXX-001">
                                    <button class="btn btn-outline-secondary" type="button" onclick="generateCode()">
                                        <i class="fas fa-magic"></i>
                                        توليد
                                    </button>
                                </div>
                                <small class="form-text text-muted">كود فريد لتمييز المنتج</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="wood_type_id" class="form-label">نوع الخشب *</label>
                                <select class="form-select" id="wood_type_id" name="wood_type_id" required>
                                    <option value="">اختر نوع الخشب</option>
                                    {% for wood_type in wood_types %}
                                    <option value="{{ wood_type.id }}">{{ wood_type.name_ar }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quality_grade" class="form-label">درجة الجودة</label>
                                <select class="form-select" id="quality_grade" name="quality_grade">
                                    <option value="">اختر درجة الجودة</option>
                                    <option value="ممتاز">ممتاز</option>
                                    <option value="جيد جداً">جيد جداً</option>
                                    <option value="جيد">جيد</option>
                                    <option value="متوسط">متوسط</option>
                                    <option value="تجاري">تجاري</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-ruler me-2"></i>
                                المقاسات والأبعاد
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="length" class="form-label">الطول (متر)</label>
                                        <input type="number" class="form-control" id="length" name="length" 
                                               step="0.01" min="0" placeholder="2.00">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="width" class="form-label">العرض (متر)</label>
                                        <input type="number" class="form-control" id="width" name="width" 
                                               step="0.01" min="0" placeholder="4.00">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="thickness" class="form-label">السماكة (متر)</label>
                                        <input type="number" class="form-control" id="thickness" name="thickness" 
                                               step="0.001" min="0" placeholder="0.050">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cubes me-2"></i>
                                الكمية والمخزون
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="quantity" class="form-label">الكمية الحالية *</label>
                                        <input type="number" class="form-control" id="quantity" name="quantity" 
                                               step="0.01" min="0" required placeholder="100">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="unit" class="form-label">الوحدة *</label>
                                        <select class="form-select" id="unit" name="unit" required>
                                            <option value="قطعة">قطعة</option>
                                            <option value="متر مكعب">متر مكعب</option>
                                            <option value="متر مربع">متر مربع</option>
                                            <option value="متر">متر</option>
                                            <option value="كيلوجرام">كيلوجرام</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="min_quantity" class="form-label">الحد الأدنى للكمية *</label>
                                        <input type="number" class="form-control" id="min_quantity" name="min_quantity" 
                                               step="0.01" min="0" required placeholder="20">
                                        <small class="form-text text-muted">سيتم التنبيه عند الوصول لهذا الحد</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-dollar-sign me-2"></i>
                                التسعير
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="cost_price" class="form-label">سعر التكلفة (ر.س)</label>
                                        <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                               step="0.01" min="0" placeholder="25.00">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="selling_price" class="form-label">سعر البيع (ر.س) *</label>
                                        <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                               step="0.01" min="0" required placeholder="35.00">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>هامش الربح:</strong> 
                                        <span id="profit_margin">سيتم حسابه تلقائياً</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">موقع التخزين</label>
                                <input type="text" class="form-control" id="location" name="location" 
                                       placeholder="المخزن الرئيسي - رف A1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        المنتج نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي ملاحظات إضافية حول المنتج..."></textarea>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ المنتج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>كود المنتج</h6>
                    <p class="mb-0">استخدم كود فريد لكل منتج لسهولة التتبع والبحث</p>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>الحد الأدنى</h6>
                    <p class="mb-0">حدد الحد الأدنى للكمية للحصول على تنبيهات عند نقص المخزون</p>
                </div>
                
                <div class="alert alert-success">
                    <h6><i class="fas fa-dollar-sign me-2"></i>التسعير</h6>
                    <p class="mb-0">تأكد من إدخال سعر التكلفة وسعر البيع لحساب الأرباح بدقة</p>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    حاسبة سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <label class="form-label">حجم المنتج (متر مكعب):</label>
                    <div class="alert alert-light mb-0">
                        <strong id="volume_calc">0.000</strong> متر مكعب
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// توليد كود المنتج تلقائياً
function generateCode() {
    const timestamp = new Date().getTime().toString().slice(-8);
    const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0');
    const code = `PRD-${timestamp}-${randomNum}`;
    document.getElementById('code').value = code;
}

// حساب هامش الربح
function calculateProfitMargin() {
    const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (costPrice > 0 && sellingPrice > 0) {
        const margin = ((sellingPrice - costPrice) / costPrice * 100).toFixed(2);
        const profit = (sellingPrice - costPrice).toFixed(2);
        document.getElementById('profit_margin').innerHTML = 
            `${margin}% (${profit} ر.س ربح لكل وحدة)`;
    } else {
        document.getElementById('profit_margin').innerHTML = 'سيتم حسابه تلقائياً';
    }
}

// حساب الحجم
function calculateVolume() {
    const length = parseFloat(document.getElementById('length').value) || 0;
    const width = parseFloat(document.getElementById('width').value) || 0;
    const thickness = parseFloat(document.getElementById('thickness').value) || 0;
    
    const volume = (length * width * thickness).toFixed(3);
    document.getElementById('volume_calc').textContent = volume;
}

// ربط الأحداث
document.getElementById('cost_price').addEventListener('input', calculateProfitMargin);
document.getElementById('selling_price').addEventListener('input', calculateProfitMargin);

document.getElementById('length').addEventListener('input', calculateVolume);
document.getElementById('width').addEventListener('input', calculateVolume);
document.getElementById('thickness').addEventListener('input', calculateVolume);

// التحقق من صحة النموذج
document.getElementById('addProductForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const code = document.getElementById('code').value.trim();
    const woodType = document.getElementById('wood_type_id').value;
    const quantity = parseFloat(document.getElementById('quantity').value);
    const minQuantity = parseFloat(document.getElementById('min_quantity').value);
    const sellingPrice = parseFloat(document.getElementById('selling_price').value);
    
    if (!name || !code || !woodType) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    if (quantity < 0 || minQuantity < 0 || sellingPrice <= 0) {
        e.preventDefault();
        alert('يرجى إدخال قيم صحيحة للكميات والأسعار');
        return false;
    }
    
    if (minQuantity > quantity) {
        if (!confirm('الحد الأدنى أكبر من الكمية الحالية. هل تريد المتابعة؟')) {
            e.preventDefault();
            return false;
        }
    }
});

// تحميل البيانات الأولية
document.addEventListener('DOMContentLoaded', function() {
    calculateProfitMargin();
    calculateVolume();
});
</script>
{% endblock %}
