{% extends "base.html" %}

{% block title %}فاتورة مبيعات جديدة - نظام إدارة المخازن{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus-circle me-2"></i>
        فاتورة مبيعات جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('sales_invoices') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<form id="salesInvoiceForm" method="POST" action="/sales-invoices/save">
    <div class="row">
        <div class="col-lg-8">
            <!-- معلومات الفاتورة الأساسية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الفاتورة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="invoice_number" class="form-label">رقم الفاتورة *</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                           required readonly value="INV-{{ moment().format('YYYYMMDDHHmmss') }}">
                                    <button class="btn btn-outline-secondary" type="button" onclick="generateInvoiceNumber()">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">العميل *</label>
                                <select class="form-select" id="customer_id" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    {% for customer in customers %}
                                    <option value="{{ customer.id }}" 
                                            data-phone="{{ customer.phone or '' }}"
                                            data-address="{{ customer.address or '' }}">
                                        {{ customer.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="invoice_date" class="form-label">تاريخ الفاتورة *</label>
                                <input type="date" class="form-control" id="invoice_date" name="invoice_date" 
                                       required value="{{ moment().format('YYYY-MM-DD') }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="due_date" name="due_date">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عناصر الفاتورة -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        عناصر الفاتورة
                    </h5>
                    <button type="button" class="btn btn-sm btn-primary" onclick="addInvoiceItem()">
                        <i class="fas fa-plus"></i>
                        إضافة عنصر
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="invoiceItemsTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="30%">المنتج</th>
                                    <th width="15%">الكمية</th>
                                    <th width="15%">السعر</th>
                                    <th width="15%">الإجمالي</th>
                                    <th width="10%">الوحدة</th>
                                    <th width="10%">حذف</th>
                                </tr>
                            </thead>
                            <tbody id="invoiceItemsBody">
                                <!-- سيتم إضافة العناصر هنا ديناميكياً -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="alert alert-info mt-3" id="noItemsAlert">
                        <i class="fas fa-info-circle me-2"></i>
                        لم يتم إضافة أي عناصر للفاتورة بعد. اضغط على "إضافة عنصر" لبدء إضافة المنتجات.
                    </div>
                </div>
            </div>

            <!-- الحسابات والضرائب -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        الحسابات والضرائب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                       step="0.01" min="0" max="100" value="15" onchange="calculateTotals()">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="discount_rate" class="form-label">معدل الخصم (%)</label>
                                <input type="number" class="form-control" id="discount_rate" name="discount_rate" 
                                       step="0.01" min="0" max="100" value="0" onchange="calculateTotals()">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        ملاحظات
                    </h5>
                </div>
                <div class="card-body">
                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                              placeholder="أي ملاحظات إضافية للفاتورة..."></textarea>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- معلومات العميل -->
            <div class="card mb-4" id="customerInfoCard" style="display: none;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        معلومات العميل
                    </h6>
                </div>
                <div class="card-body">
                    <div id="customerInfo">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                </div>
            </div>

            <!-- ملخص الفاتورة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        ملخص الفاتورة
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td>المجموع الفرعي:</td>
                            <td class="text-end"><span id="subtotalDisplay">0.00</span> ر.س</td>
                        </tr>
                        <tr>
                            <td>الخصم:</td>
                            <td class="text-end"><span id="discountDisplay">0.00</span> ر.س</td>
                        </tr>
                        <tr>
                            <td>الضريبة:</td>
                            <td class="text-end"><span id="taxDisplay">0.00</span> ر.س</td>
                        </tr>
                        <tr class="table-primary">
                            <td><strong>الإجمالي:</strong></td>
                            <td class="text-end"><strong><span id="totalDisplay">0.00</span> ر.س</strong></td>
                        </tr>
                    </table>
                    
                    <!-- حقول مخفية للحسابات -->
                    <input type="hidden" id="subtotal" name="subtotal" value="0">
                    <input type="hidden" id="tax_amount" name="tax_amount" value="0">
                    <input type="hidden" id="discount_amount" name="discount_amount" value="0">
                    <input type="hidden" id="total_amount" name="total_amount" value="0">
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" name="action" value="draft" class="btn btn-secondary">
                            <i class="fas fa-save"></i>
                            حفظ كمسودة
                        </button>
                        <button type="submit" name="action" value="confirm" class="btn btn-success">
                            <i class="fas fa-check"></i>
                            حفظ وتأكيد
                        </button>
                        <a href="{{ url_for('sales_invoices') }}" class="btn btn-outline-danger">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- قالب عنصر الفاتورة (مخفي) -->
<template id="invoiceItemTemplate">
    <tr class="invoice-item">
        <td>
            <select class="form-select product-select" name="product_id[]" required onchange="updateItemPrice(this)">
                <option value="">اختر المنتج</option>
                {% for product in products %}
                <option value="{{ product.id }}" 
                        data-price="{{ product.selling_price }}"
                        data-unit="{{ product.unit }}"
                        data-stock="{{ product.quantity }}">
                    {{ product.name }} ({{ product.code }})
                </option>
                {% endfor %}
            </select>
        </td>
        <td>
            <input type="number" class="form-control quantity-input" name="quantity[]" 
                   step="0.01" min="0.01" required onchange="calculateItemTotal(this)">
        </td>
        <td>
            <input type="number" class="form-control price-input" name="unit_price[]" 
                   step="0.01" min="0.01" required onchange="calculateItemTotal(this)">
        </td>
        <td>
            <input type="number" class="form-control total-input" name="total_price[]" 
                   step="0.01" readonly>
        </td>
        <td>
            <span class="unit-display">-</span>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeInvoiceItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    </tr>
</template>
{% endblock %}

{% block extra_js %}
<script>
let itemCounter = 0;

// توليد رقم فاتورة جديد
function generateInvoiceNumber() {
    const now = new Date();
    const timestamp = now.getFullYear().toString() + 
                     (now.getMonth() + 1).toString().padStart(2, '0') + 
                     now.getDate().toString().padStart(2, '0') + 
                     now.getHours().toString().padStart(2, '0') + 
                     now.getMinutes().toString().padStart(2, '0') + 
                     now.getSeconds().toString().padStart(2, '0');
    document.getElementById('invoice_number').value = 'INV-' + timestamp;
}

// إضافة عنصر جديد للفاتورة
function addInvoiceItem() {
    const template = document.getElementById('invoiceItemTemplate');
    const tbody = document.getElementById('invoiceItemsBody');
    const clone = template.content.cloneNode(true);
    
    tbody.appendChild(clone);
    itemCounter++;
    
    // إخفاء رسالة عدم وجود عناصر
    document.getElementById('noItemsAlert').style.display = 'none';
    
    calculateTotals();
}

// حذف عنصر من الفاتورة
function removeInvoiceItem(button) {
    const row = button.closest('tr');
    row.remove();
    itemCounter--;
    
    // إظهار رسالة عدم وجود عناصر إذا لم تعد هناك عناصر
    if (itemCounter === 0) {
        document.getElementById('noItemsAlert').style.display = 'block';
    }
    
    calculateTotals();
}

// تحديث سعر العنصر عند اختيار المنتج
function updateItemPrice(select) {
    const row = select.closest('tr');
    const option = select.selectedOptions[0];
    
    if (option.value) {
        const price = parseFloat(option.dataset.price) || 0;
        const unit = option.dataset.unit || '';
        const stock = parseFloat(option.dataset.stock) || 0;
        
        row.querySelector('.price-input').value = price.toFixed(2);
        row.querySelector('.unit-display').textContent = unit;
        
        // تحديث الحد الأقصى للكمية
        const quantityInput = row.querySelector('.quantity-input');
        quantityInput.max = stock;
        quantityInput.title = `المتوفر في المخزن: ${stock} ${unit}`;
        
        calculateItemTotal(row.querySelector('.quantity-input'));
    }
}

// حساب إجمالي العنصر
function calculateItemTotal(input) {
    const row = input.closest('tr');
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const price = parseFloat(row.querySelector('.price-input').value) || 0;
    const total = quantity * price;
    
    row.querySelector('.total-input').value = total.toFixed(2);
    calculateTotals();
}

// حساب إجماليات الفاتورة
function calculateTotals() {
    const totalInputs = document.querySelectorAll('.total-input');
    let subtotal = 0;
    
    totalInputs.forEach(input => {
        subtotal += parseFloat(input.value) || 0;
    });
    
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    const discountRate = parseFloat(document.getElementById('discount_rate').value) || 0;
    
    const discountAmount = (subtotal * discountRate) / 100;
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = (taxableAmount * taxRate) / 100;
    const total = taxableAmount + taxAmount;
    
    // تحديث العرض
    document.getElementById('subtotalDisplay').textContent = subtotal.toFixed(2);
    document.getElementById('discountDisplay').textContent = discountAmount.toFixed(2);
    document.getElementById('taxDisplay').textContent = taxAmount.toFixed(2);
    document.getElementById('totalDisplay').textContent = total.toFixed(2);
    
    // تحديث الحقول المخفية
    document.getElementById('subtotal').value = subtotal.toFixed(2);
    document.getElementById('discount_amount').value = discountAmount.toFixed(2);
    document.getElementById('tax_amount').value = taxAmount.toFixed(2);
    document.getElementById('total_amount').value = total.toFixed(2);
}

// عرض معلومات العميل
function showCustomerInfo() {
    const select = document.getElementById('customer_id');
    const option = select.selectedOptions[0];
    const card = document.getElementById('customerInfoCard');
    const info = document.getElementById('customerInfo');
    
    if (option.value) {
        const phone = option.dataset.phone || 'غير محدد';
        const address = option.dataset.address || 'غير محدد';
        
        info.innerHTML = `
            <p class="mb-1"><strong>الاسم:</strong> ${option.text}</p>
            <p class="mb-1"><strong>الهاتف:</strong> ${phone}</p>
            <p class="mb-0"><strong>العنوان:</strong> ${address}</p>
        `;
        card.style.display = 'block';
    } else {
        card.style.display = 'none';
    }
}

// التحقق من صحة النموذج
function validateForm() {
    const customerId = document.getElementById('customer_id').value;
    const items = document.querySelectorAll('.invoice-item');
    
    if (!customerId) {
        alert('يرجى اختيار العميل');
        return false;
    }
    
    if (items.length === 0) {
        alert('يرجى إضافة عنصر واحد على الأقل للفاتورة');
        return false;
    }
    
    // التحقق من صحة العناصر
    for (let item of items) {
        const productId = item.querySelector('.product-select').value;
        const quantity = parseFloat(item.querySelector('.quantity-input').value);
        const price = parseFloat(item.querySelector('.price-input').value);
        
        if (!productId || !quantity || !price) {
            alert('يرجى ملء جميع بيانات العناصر');
            return false;
        }
        
        if (quantity <= 0 || price <= 0) {
            alert('يرجى إدخال قيم صحيحة للكمية والسعر');
            return false;
        }
    }
    
    return true;
}

// ربط الأحداث
document.getElementById('customer_id').addEventListener('change', showCustomerInfo);
document.getElementById('salesInvoiceForm').addEventListener('submit', function(e) {
    if (!validateForm()) {
        e.preventDefault();
        return false;
    }
});

// إضافة عنصر افتراضي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    addInvoiceItem();
});
</script>
{% endblock %}
