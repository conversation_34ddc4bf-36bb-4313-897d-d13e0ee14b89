{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة المخازن{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar-alt"></i>
                اليوم: {{ moment().strftime('%Y-%m-%d') if moment else '' }}
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي المنتجات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_products or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-boxes fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            مبيعات اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ today_sales or 0 }} ر.س
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            العملاء النشطين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ active_customers or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            تنبيهات المخزون
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ low_stock_alerts or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Sales Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line me-2"></i>
                    مبيعات الشهر الحالي
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="salesChart" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Pie Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع المنتجات
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="productsChart" width="100%" height="50"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities and Low Stock -->
<div class="row">
    <!-- Recent Sales -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-receipt me-2"></i>
                    آخر المبيعات
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if recent_sales %}
                                {% for sale in recent_sales %}
                                <tr>
                                    <td>{{ sale.invoice_number }}</td>
                                    <td>{{ sale.customer.name }}</td>
                                    <td>{{ sale.total_amount }} ر.س</td>
                                    <td>{{ sale.invoice_date.strftime('%Y-%m-%d') }}</td>
                                </tr>
                                {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center text-muted">لا توجد مبيعات حديثة</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Products -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    منتجات تحتاج إعادة تموين
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية الحالية</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if low_stock_products %}
                                {% for product in low_stock_products %}
                                <tr>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.quantity }} {{ product.unit }}</td>
                                    <td>{{ product.min_quantity }} {{ product.unit }}</td>
                                    <td>
                                        <span class="badge bg-warning">نقص مخزون</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center text-success">جميع المنتجات متوفرة</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/sales-invoices/new" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus-circle mb-2"></i><br>
                            فاتورة مبيعات جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/purchase-invoices/new" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-shopping-cart mb-2"></i><br>
                            فاتورة مشتريات جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/products/new" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-box mb-2"></i><br>
                            إضافة منتج جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/reports" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-chart-bar mb-2"></i><br>
                            عرض التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sales Chart
const salesCtx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(salesCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'المبيعات',
            data: [12000, 19000, 15000, 25000, 22000, 30000],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        }
    }
});

// Products Chart
const productsCtx = document.getElementById('productsChart').getContext('2d');
const productsChart = new Chart(productsCtx, {
    type: 'doughnut',
    data: {
        labels: ['خشب زان', 'خشب سنديان', 'خشب صنوبر', 'MDF'],
        datasets: [{
            data: [30, 25, 20, 25],
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            }
        }
    }
});
</script>
{% endblock %}
