# دليل حل المشاكل | Guide de Dépannage

## مشكلة SQLAlchemy AssertionError

### الخطأ:
```
AssertionError: Class <class 'sqlalchemy.sql.elements.SQLCoreOperations'> directly inherits TypingOnly but has additional attributes
```

### السبب:
تعارض في إصدارات SQLAlchemy مع Python الحديث ومكتبة typing-extensions.

### الحلول المتاحة:

## 🚀 الحل السريع (موصى به)

### استخدم النسخة المبسطة:
```bash
python app_basic.py
```
أو انقر مرتين على `run_basic.bat`

**المميزات:**
- ✅ لا توجد مشاكل توافق
- ✅ يعمل مع جميع إصدارات Python
- ✅ واجهة مستخدم كاملة
- ✅ قاعدة بيانات SQLite مدمجة
- ✅ جميع الوظائف الأساسية متوفرة

## 🔧 الحل التقني

### 1. إصلاح التبعيات تلقائياً:
```bash
python fix_dependencies.py
```

### 2. الإصلاح اليدوي:
```bash
# إلغاء تثبيت المكتبات المتعارضة
pip uninstall -y SQLAlchemy Flask-SQLAlchemy typing-extensions

# تثبيت الإصدارات المتوافقة
pip install typing-extensions==4.8.0
pip install SQLAlchemy==1.4.53
pip install Flask-SQLAlchemy==3.0.5

# تثبيت باقي المتطلبات
pip install -r requirements.txt
```

### 3. الإعداد الكامل:
```bash
python quick_setup.py
```

## 🐍 مشاكل Python

### Python غير مثبت:
1. حمّل Python من https://python.org
2. تأكد من إضافة Python إلى PATH
3. أعد تشغيل موجه الأوامر

### إصدار Python قديم:
```bash
python --version
```
يجب أن يكون 3.8 أو أحدث

## 📦 مشاكل المكتبات

### pip لا يعمل:
```bash
python -m ensurepip --upgrade
python -m pip install --upgrade pip
```

### مشاكل الشبكة:
```bash
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
```

## 🗄️ مشاكل قاعدة البيانات

### قاعدة البيانات تالفة:
```bash
# احذف قاعدة البيانات وأعد إنشاؤها
del warehouse_system.db
python app_simple.py
```

### للنسخة المبسطة:
```bash
del warehouse_basic.db
python app_basic.py
```

## 🌐 مشاكل المتصفح

### الصفحة لا تحمل:
1. تأكد من تشغيل النظام
2. جرب http://127.0.0.1:5000 بدلاً من localhost
3. أغلق برامج مكافحة الفيروسات مؤقتاً
4. جرب متصفح آخر

### مشاكل التصميم:
1. امسح cache المتصفح (Ctrl+F5)
2. تأكد من اتصال الإنترنت (للخطوط والأيقونات)

## 🔐 مشاكل تسجيل الدخول

### نسيت كلمة المرور:
البيانات الافتراضية:
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### إعادة تعيين كلمة المرور:
```python
# في Python shell
from werkzeug.security import generate_password_hash
import sqlite3

conn = sqlite3.connect('warehouse_basic.db')
new_password = generate_password_hash('admin123')
conn.execute('UPDATE users SET password_hash = ? WHERE username = ?', (new_password, 'admin'))
conn.commit()
conn.close()
```

## 🖥️ مشاكل Windows

### مشاكل الترميز:
```bash
chcp 65001
```

### مشاكل الصلاحيات:
- شغّل موجه الأوامر كمدير
- أو استخدم PowerShell

## 📱 مشاكل الأجهزة المحمولة

### التصميم لا يظهر بشكل صحيح:
- النظام مصمم للحاسوب أولاً
- استخدم المتصفح في وضع Desktop
- أو استخدم جهاز حاسوب

## 🆘 طلب المساعدة

### معلومات مطلوبة عند طلب المساعدة:
1. نظام التشغيل وإصداره
2. إصدار Python (`python --version`)
3. رسالة الخطأ كاملة
4. الخطوات التي قمت بها

### اختبار النظام:
```bash
python test_system.py
```

## ✅ التحقق من عمل النظام

### اختبار سريع:
1. شغّل النظام
2. افتح http://localhost:5000
3. سجل دخول بـ admin/admin123
4. تأكد من ظهور لوحة التحكم

### اختبار شامل:
```bash
python test_system.py
```

## 🔄 إعادة التثبيت الكاملة

### إذا فشل كل شيء:
```bash
# احذف جميع المكتبات
pip freeze > installed_packages.txt
pip uninstall -r installed_packages.txt -y

# إعادة التثبيت
python quick_setup.py
```

## 📞 الدعم

### للمشاكل التقنية:
- راجع هذا الدليل أولاً
- استخدم النسخة المبسطة كحل بديل
- تأكد من تحديث Python والمكتبات

### للتطوير والتخصيص:
- راجع ملف README.md
- استخدم النسخة الكاملة بعد حل مشاكل التوافق
- اقرأ التعليقات في الكود

---

**ملاحظة مهمة:** النسخة المبسطة (app_basic.py) توفر جميع الوظائف الأساسية بدون مشاكل التوافق وهي الخيار الأفضل للاستخدام الفوري.
