import os
from datetime import timedelta

class Config:
    # إعدادات أساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///warehouse_system.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # إعدادات الملفات المرفوعة
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    UPLOAD_FOLDER = 'uploads'
    
    # إعدادات اللغة
    LANGUAGES = {
        'ar': 'العربية',
        'fr': 'Français'
    }
    
    # إعدادات الضرائب الافتراضية
    DEFAULT_TAX_RATE = 15.0  # 15% ضريبة القيمة المضافة
    
    # إعدادات العملة
    CURRENCY_SYMBOL = 'ر.س'
    CURRENCY_CODE = 'SAR'
    
    # إعدادات التقارير
    REPORTS_PER_PAGE = 50
    
    # إعدادات البريد الإلكتروني (اختيارية)
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # إعدادات النسخ الاحتياطي
    BACKUP_FOLDER = 'backups'
    AUTO_BACKUP_DAYS = 7  # نسخة احتياطية كل 7 أيام

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False
    
class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
