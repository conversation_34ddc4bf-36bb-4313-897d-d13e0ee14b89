#!/usr/bin/env python3
"""
نسخة محدثة من app.py تحل مشكلة Babel
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///warehouse_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'

# إعداد دعم اللغات المتعددة (محدث)
try:
    from flask_babel import Babel
    babel = Babel()
    babel.init_app(app)
    
    @babel.localeselector
    def get_locale():
        # التحقق من اللغة المحفوظة في الجلسة
        if 'language' in session:
            return session['language']
        # اللغة الافتراضية
        return 'ar'
    
    BABEL_AVAILABLE = True
    print("✅ Babel متوفر - دعم اللغات المتعددة مفعل")
    
except ImportError:
    BABEL_AVAILABLE = False
    print("ℹ️ Babel غير متوفر - سيتم استخدام العربية فقط")
    
    # دالة بديلة لتغيير اللغة
    def get_locale():
        return session.get('language', 'ar')

# إنشاء النماذج المبسطة
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    role = db.Column(db.String(50), default='user')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    code = db.Column(db.String(50), unique=True, nullable=False)
    quantity = db.Column(db.Float, default=0)
    unit = db.Column(db.String(50), default='قطعة')
    cost_price = db.Column(db.Float, default=0)
    selling_price = db.Column(db.Float, default=0)
    min_quantity = db.Column(db.Float, default=0)
    location = db.Column(db.String(200))
    notes = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(50))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    balance = db.Column(db.Float, default=0)
    credit_limit = db.Column(db.Float, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class SalesInvoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    invoice_date = db.Column(db.Date, default=datetime.utcnow().date)
    total_amount = db.Column(db.Float, default=0)
    status = db.Column(db.String(50), default='draft')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    customer = db.relationship('Customer', backref='sales_invoices')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
@login_required
def index():
    try:
        # إحصائيات لوحة التحكم
        from sqlalchemy import func

        # إجمالي المنتجات
        total_products = Product.query.filter_by(is_active=True).count()

        # العملاء النشطين
        active_customers = Customer.query.filter_by(is_active=True).count()

        # مبيعات اليوم
        today = datetime.utcnow().date()
        today_sales = db.session.query(func.sum(SalesInvoice.total_amount)).filter(
            SalesInvoice.invoice_date == today,
            SalesInvoice.status == 'confirmed'
        ).scalar() or 0

        # المنتجات التي تحتاج إعادة تموين
        low_stock_products = Product.query.filter(
            Product.quantity <= Product.min_quantity,
            Product.is_active == True
        ).limit(5).all()

        # آخر المبيعات
        recent_sales = SalesInvoice.query.filter_by(status='confirmed').order_by(
            SalesInvoice.created_at.desc()
        ).limit(5).all()

    except Exception as e:
        print(f"خطأ في جلب البيانات: {e}")
        total_products = 0
        active_customers = 0
        today_sales = 0
        low_stock_products = []
        recent_sales = []

    return render_template('dashboard.html',
                         total_products=total_products,
                         today_sales=today_sales,
                         active_customers=active_customers,
                         low_stock_alerts=len(low_stock_products),
                         low_stock_products=low_stock_products,
                         recent_sales=recent_sales,
                         babel_available=BABEL_AVAILABLE)

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        try:
            user = User.query.filter_by(username=username).first()
            
            if user and check_password_hash(user.password_hash, password):
                login_user(user)
                flash('تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('index'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            flash('حدث خطأ في تسجيل الدخول', 'error')
    
    return render_template('login.html', babel_available=BABEL_AVAILABLE)

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

# تغيير اللغة
@app.route('/set_language/<language>')
def set_language(language=None):
    session['language'] = language
    flash(f'تم تغيير اللغة إلى {language}', 'info')
    return redirect(request.referrer or url_for('index'))

# صفحة المنتجات
@app.route('/products')
@login_required
def products():
    try:
        products = Product.query.filter_by(is_active=True).order_by(Product.created_at.desc()).all()
    except Exception as e:
        print(f"خطأ في جلب المنتجات: {e}")
        products = []
        flash('حدث خطأ في جلب المنتجات', 'error')
    
    return render_template('products/list.html', products=products, babel_available=BABEL_AVAILABLE)

# صفحة العملاء
@app.route('/customers')
@login_required
def customers():
    try:
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.created_at.desc()).all()
    except Exception as e:
        print(f"خطأ في جلب العملاء: {e}")
        customers = []
        flash('حدث خطأ في جلب العملاء', 'error')
    
    return render_template('customers/list.html', customers=customers, babel_available=BABEL_AVAILABLE)

# API معلومات النظام
@app.route('/api/system_info')
@login_required
def system_info():
    try:
        info = {
            'total_products': Product.query.filter_by(is_active=True).count(),
            'total_customers': Customer.query.filter_by(is_active=True).count(),
            'babel_available': BABEL_AVAILABLE,
            'current_language': get_locale(),
            'status': 'يعمل بنجاح'
        }
    except Exception as e:
        info = {
            'error': str(e),
            'status': 'خطأ'
        }
    
    return jsonify(info)

if __name__ == '__main__':
    with app.app_context():
        try:
            db.create_all()
            
            # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
            if not User.query.filter_by(username='admin').first():
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='المدير العام',
                    role='admin',
                    password_hash=generate_password_hash('admin123')
                )
                db.session.add(admin_user)
                
                # إضافة بيانات تجريبية
                sample_products = [
                    Product(name='لوح خشب زان', code='PRD-001', quantity=50, selling_price=35, min_quantity=10),
                    Product(name='لوح خشب سنديان', code='PRD-002', quantity=30, selling_price=45, min_quantity=15),
                    Product(name='لوح MDF', code='PRD-003', quantity=100, selling_price=22, min_quantity=20)
                ]
                
                sample_customers = [
                    Customer(name='شركة الأثاث الحديث', phone='0501234567', email='<EMAIL>'),
                    Customer(name='مصنع الخشب الذهبي', phone='0509876543', email='<EMAIL>')
                ]
                
                for product in sample_products:
                    db.session.add(product)
                
                for customer in sample_customers:
                    db.session.add(customer)
                
                db.session.commit()
                print("✅ تم إنشاء البيانات الافتراضية")
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
    
    print("🚀 تم تشغيل نظام إدارة المخازن (نسخة محدثة)")
    print("📍 الرابط: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("✅ تم حل مشكلة Babel!")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
