#!/usr/bin/env python3
"""
اختبار سريع للنظام
"""

import requests
import time

def test_system():
    """اختبار سريع للنظام"""
    base_url = 'http://localhost:5000'
    
    print("🧪 اختبار سريع للنظام...")
    
    try:
        # اختبار الصفحة الرئيسية
        print("1️⃣ اختبار الصفحة الرئيسية...")
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ الصفحة الرئيسية تعمل")
        else:
            print(f"❌ مشكلة في الصفحة الرئيسية: {response.status_code}")
            
        # اختبار صفحة تسجيل الدخول
        print("2️⃣ اختبار صفحة تسجيل الدخول...")
        response = requests.get(f"{base_url}/login", timeout=5)
        if response.status_code == 200 and "تسجيل الدخول" in response.text:
            print("✅ صفحة تسجيل الدخول تعمل")
        else:
            print("❌ مشكلة في صفحة تسجيل الدخول")
            
        # اختبار تسجيل الدخول
        print("3️⃣ اختبار تسجيل الدخول...")
        session = requests.Session()
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        response = session.post(f"{base_url}/login", data=login_data, timeout=5)
        if response.status_code in [200, 302]:
            print("✅ تسجيل الدخول يعمل")
            
            # اختبار صفحة المنتجات
            print("4️⃣ اختبار صفحة المنتجات...")
            response = session.get(f"{base_url}/products", timeout=5)
            if response.status_code == 200:
                print("✅ صفحة المنتجات تعمل")
            else:
                print(f"❌ مشكلة في صفحة المنتجات: {response.status_code}")
                
            # اختبار صفحة العملاء
            print("5️⃣ اختبار صفحة العملاء...")
            response = session.get(f"{base_url}/customers", timeout=5)
            if response.status_code == 200:
                print("✅ صفحة العملاء تعمل")
            else:
                print(f"❌ مشكلة في صفحة العملاء: {response.status_code}")
                
            # اختبار API
            print("6️⃣ اختبار API...")
            response = session.get(f"{base_url}/api/system_info", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API يعمل - المنتجات: {data.get('total_products', 0)}")
            else:
                print(f"❌ مشكلة في API: {response.status_code}")
        else:
            print("❌ فشل في تسجيل الدخول")
            
        print("\n🎉 انتهى الاختبار السريع!")
        
    except requests.exceptions.ConnectionError:
        print("❌ لا يمكن الاتصال بالنظام. تأكد من تشغيله على http://localhost:5000")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_system()
