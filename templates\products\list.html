{% extends "base.html" %}

{% block title %}إدارة المنتجات - نظام إدارة المخازن{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-boxes me-2"></i>
        إدارة المنتجات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة منتج جديد
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ products|length }}</h4>
                        <p class="mb-0">إجمالي المنتجات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ products|selectattr("quantity", "gt", "min_quantity")|list|length }}</h4>
                        <p class="mb-0">منتجات متوفرة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ products|selectattr("quantity", "le", "min_quantity")|list|length }}</h4>
                        <p class="mb-0">تحتاج إعادة تموين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ products|sum(attribute="quantity")|round(2) }}</h4>
                        <p class="mb-0">إجمالي الكمية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-cubes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول المنتجات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المنتجات
        </h5>
    </div>
    <div class="card-body">
        {% if products %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>الكود</th>
                        <th>اسم المنتج</th>
                        <th>نوع الخشب</th>
                        <th>المقاسات</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>سعر البيع</th>
                        <th>الموقع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>
                            <code>{{ product.code }}</code>
                        </td>
                        <td>
                            <strong>{{ product.name }}</strong>
                            {% if product.quality_grade %}
                            <br><small class="text-muted">{{ product.quality_grade }}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.wood_type %}
                                {{ product.wood_type.name_ar }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.length and product.width %}
                                {{ product.length }}×{{ product.width }}
                                {% if product.thickness %}×{{ product.thickness }}{% endif %}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.quantity <= product.min_quantity %}
                                <span class="badge bg-danger">{{ product.quantity }}</span>
                            {% elif product.quantity <= (product.min_quantity * 1.5) %}
                                <span class="badge bg-warning">{{ product.quantity }}</span>
                            {% else %}
                                <span class="badge bg-success">{{ product.quantity }}</span>
                            {% endif %}
                        </td>
                        <td>{{ product.unit }}</td>
                        <td>
                            <strong>{{ "%.2f"|format(product.selling_price) }} ر.س</strong>
                        </td>
                        <td>
                            {% if product.location %}
                                <small>{{ product.location }}</small>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.quantity <= product.min_quantity %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    نقص مخزون
                                </span>
                            {% elif product.quantity <= (product.min_quantity * 1.5) %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-exclamation-circle"></i>
                                    تحذير
                                </span>
                            {% else %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i>
                                    متوفر
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد منتجات</h5>
            <p class="text-muted">ابدأ بإضافة منتجات جديدة إلى المخزن</p>
            <button type="button" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                إضافة منتج جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- معلومات إضافية -->
{% if products %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    منتجات تحتاج إعادة تموين
                </h6>
            </div>
            <div class="card-body">
                {% set low_stock = products|selectattr("quantity", "le", "min_quantity")|list %}
                {% if low_stock %}
                <ul class="list-unstyled mb-0">
                    {% for product in low_stock %}
                    <li class="mb-2">
                        <strong>{{ product.name }}</strong>
                        <br>
                        <small class="text-muted">
                            الكمية الحالية: {{ product.quantity }} {{ product.unit }} |
                            الحد الأدنى: {{ product.min_quantity }} {{ product.unit }}
                        </small>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-success mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    جميع المنتجات متوفرة بكميات كافية
                </p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie text-info me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ "%.2f"|format(products|sum(attribute="selling_price")) }}</h4>
                        <small class="text-muted">إجمالي قيمة المخزون</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ products|selectattr("is_active")|list|length }}</h4>
                        <small class="text-muted">منتجات نشطة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// إضافة تفاعلية للجدول
$(document).ready(function() {
    // تفعيل tooltips
    $('[title]').tooltip();
    
    // تأكيد الحذف
    $('.btn-outline-danger').click(function() {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            // هنا يمكن إضافة كود الحذف
            alert('سيتم تطوير وظيفة الحذف قريباً');
        }
    });
});
</script>
{% endblock %}
