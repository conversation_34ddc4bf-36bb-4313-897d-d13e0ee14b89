#!/usr/bin/env python3
"""
ملف إصلاح مشاكل التبعيات
يقوم بحل مشاكل التوافق بين إصدارات المكتبات
"""

import subprocess
import sys
import os

def run_command(command, description):
    """تشغيل أمر مع عرض الوصف"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - تم بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - فشل: {e}")
        if e.stderr:
            print(f"تفاصيل الخطأ: {e.stderr}")
        return False

def fix_dependencies():
    """إصلاح مشاكل التبعيات"""
    print("🔧 إصلاح مشاكل التبعيات...")
    print("=" * 50)
    
    # إلغاء تثبيت المكتبات المتعارضة
    print("\n1️⃣ إلغاء تثبيت المكتبات المتعارضة...")
    uninstall_commands = [
        "pip uninstall -y SQLAlchemy",
        "pip uninstall -y Flask-SQLAlchemy", 
        "pip uninstall -y typing-extensions"
    ]
    
    for cmd in uninstall_commands:
        run_command(cmd, f"تشغيل: {cmd}")
    
    # تثبيت الإصدارات المتوافقة
    print("\n2️⃣ تثبيت الإصدارات المتوافقة...")
    install_commands = [
        "pip install typing-extensions==4.8.0",
        "pip install SQLAlchemy==1.4.53",
        "pip install Flask-SQLAlchemy==3.0.5"
    ]
    
    for cmd in install_commands:
        if not run_command(cmd, f"تشغيل: {cmd}"):
            return False
    
    # تثبيت باقي المتطلبات
    print("\n3️⃣ تثبيت باقي المتطلبات...")
    if os.path.exists("requirements.txt"):
        return run_command("pip install -r requirements.txt", "تثبيت المتطلبات من الملف")
    else:
        print("⚠️ ملف requirements.txt غير موجود")
        return False

def verify_installation():
    """التحقق من صحة التثبيت"""
    print("\n🔍 التحقق من صحة التثبيت...")
    
    try:
        import flask
        print(f"✅ Flask {flask.__version__}")
        
        import sqlalchemy
        print(f"✅ SQLAlchemy {sqlalchemy.__version__}")
        
        import flask_sqlalchemy
        print(f"✅ Flask-SQLAlchemy {flask_sqlalchemy.__version__}")
        
        import flask_login
        print(f"✅ Flask-Login {flask_login.__version__}")
        
        print("\n🎉 جميع المكتبات مثبتة بشكل صحيح!")
        return True
        
    except ImportError as e:
        print(f"❌ مشكلة في استيراد المكتبة: {e}")
        return False

def test_app_import():
    """اختبار استيراد التطبيق"""
    print("\n🧪 اختبار استيراد التطبيق...")
    
    try:
        # محاولة استيراد التطبيق
        sys.path.insert(0, '.')
        import app_simple
        print("✅ تم استيراد التطبيق بنجاح")
        return True
    except Exception as e:
        print(f"❌ مشكلة في استيراد التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح مشاكل التبعيات")
    print("نظام إدارة المخازن والمحاسبة")
    print("=" * 50)
    
    # إصلاح التبعيات
    if not fix_dependencies():
        print("\n❌ فشل في إصلاح التبعيات")
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من التثبيت
    if not verify_installation():
        print("\n❌ مشكلة في التحقق من التثبيت")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار التطبيق
    if test_app_import():
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("\n📋 يمكنك الآن تشغيل النظام باستخدام:")
        print("   python app_simple.py")
        print("   أو انقر مرتين على start_system.bat")
    else:
        print("\n⚠️ هناك مشاكل في التطبيق تحتاج مراجعة")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
