@echo off
chcp 65001 >nul
title نظام إدارة المخازن والمحاسبة

echo.
echo ========================================
echo    نظام إدارة المخازن والمحاسبة
echo    Warehouse Management System
echo ========================================
echo.

echo 🔍 فحص متطلبات النظام...

:: فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

:: فحص وجود ملف التطبيق
if not exist "app_simple.py" (
    echo ❌ ملف التطبيق app_simple.py غير موجود
    pause
    exit /b 1
)

echo ✅ ملف التطبيق موجود

:: فحص وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ⚠️ ملف requirements.txt غير موجود
    echo سيتم المحاولة بدون تثبيت المتطلبات
) else (
    echo 📦 تثبيت المتطلبات...
    pip install -r requirements.txt >nul 2>&1
    if errorlevel 1 (
        echo ⚠️ تحذير: مشكلة في تثبيت بعض المتطلبات
    ) else (
        echo ✅ تم تثبيت المتطلبات
    )
)

echo.
echo 🚀 بدء تشغيل النظام...
echo.
echo 📍 سيتم فتح النظام على: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo ⏹️ لإيقاف النظام اضغط Ctrl+C
echo.

:: تشغيل النظام
python app_simple.py

echo.
echo 📴 تم إيقاف النظام
pause
