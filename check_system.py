#!/usr/bin/env python3
"""
فحص سريع للنظام بدون مكتبات خارجية
"""

import os
import sys

def check_files():
    """فحص وجود الملفات المطلوبة"""
    print("📁 فحص الملفات...")
    
    required_files = [
        'app_simple_fixed.py',
        'templates/dashboard_simple.html',
        'templates/login_simple.html'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - غير موجود")
            all_exist = False
    
    return all_exist

def check_python():
    """فحص إصدار Python"""
    print("\n🐍 فحص Python...")
    
    version = sys.version_info
    print(f"إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 8):
        print("✅ إصدار Python مناسب")
        return True
    else:
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False

def check_modules():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات...")
    
    modules = ['flask', 'werkzeug', 'sqlite3']
    all_available = True
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - غير مثبت")
            all_available = False
    
    return all_available

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️ فحص قاعدة البيانات...")
    
    db_files = [
        'warehouse_simple_fixed.db',
        'warehouse_fixed.db',
        'warehouse_basic.db'
    ]
    
    found = False
    for db_file in db_files:
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            print(f"✅ {db_file} ({size} bytes)")
            found = True
        else:
            print(f"ℹ️ {db_file} - سيتم إنشاؤها عند التشغيل")
    
    if not found:
        print("ℹ️ لا توجد قاعدة بيانات - سيتم إنشاؤها تلقائياً")
    
    return True

def check_port():
    """فحص المنفذ 5000"""
    print("\n🌐 فحص المنفذ...")
    
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if result == 0:
            print("✅ المنفذ 5000 مستخدم (النظام يعمل)")
            return True
        else:
            print("ℹ️ المنفذ 5000 متاح (النظام غير مشغل)")
            return False
    except:
        print("ℹ️ لا يمكن فحص المنفذ")
        return False

def show_instructions():
    """عرض تعليمات التشغيل"""
    print("\n" + "=" * 50)
    print("📋 تعليمات التشغيل:")
    print("=" * 50)
    
    print("\n🚀 للتشغيل السريع:")
    print("   انقر مرتين على: run_simple_fixed.bat")
    
    print("\n🔧 للتشغيل اليدوي:")
    print("   1. pip install Flask Werkzeug")
    print("   2. python app_simple_fixed.py")
    
    print("\n🌐 للوصول للنظام:")
    print("   1. افتح المتصفح")
    print("   2. اذهب إلى: http://localhost:5000")
    print("   3. اسم المستخدم: admin")
    print("   4. كلمة المرور: admin123")
    
    print("\n🔧 لحل مشكلة المكتبات:")
    print("   pip install Flask==2.3.3 Werkzeug==2.3.7")

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص نظام إدارة المخازن")
    print("=" * 40)
    
    # فحص جميع المتطلبات
    files_ok = check_files()
    python_ok = check_python()
    modules_ok = check_modules()
    db_ok = check_database()
    port_status = check_port()
    
    print("\n" + "=" * 40)
    print("📊 نتائج الفحص:")
    print("=" * 40)
    
    if files_ok and python_ok and modules_ok:
        print("🎉 النظام جاهز للتشغيل!")
        
        if port_status:
            print("✅ النظام يعمل حالياً")
            print("🌐 افتح المتصفح على: http://localhost:5000")
        else:
            print("ℹ️ النظام غير مشغل")
            print("🚀 شغّل: run_simple_fixed.bat")
    else:
        print("⚠️ هناك مشاكل تحتاج حل:")
        
        if not files_ok:
            print("   - ملفات مفقودة")
        if not python_ok:
            print("   - إصدار Python قديم")
        if not modules_ok:
            print("   - مكتبات غير مثبتة")
    
    show_instructions()

if __name__ == "__main__":
    main()
