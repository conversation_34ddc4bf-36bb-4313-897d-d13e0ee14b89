{% extends "base.html" %}

{% block title %}إدارة أنواع الخشب - نظام إدارة المخازن{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tree me-2"></i>
        إدارة أنواع الخشب
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWoodTypeModal">
                <i class="fas fa-plus"></i>
                إضافة نوع جديد
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات أنواع الخشب -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ wood_types|length }}</h4>
                        <p class="mb-0">إجمالي الأنواع</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tree fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ wood_types|selectattr("is_active")|list|length }}</h4>
                        <p class="mb-0">أنواع نشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ products_count or 0 }}</h4>
                        <p class="mb-0">منتجات مرتبطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ wood_types|rejectattr("is_active")|list|length }}</h4>
                        <p class="mb-0">أنواع غير نشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pause-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول أنواع الخشب -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة أنواع الخشب
        </h5>
    </div>
    <div class="card-body">
        {% if wood_types %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>الاسم بالعربية</th>
                        <th>الاسم بالفرنسية</th>
                        <th>الوصف</th>
                        <th>عدد المنتجات</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for wood_type in wood_types %}
                    <tr>
                        <td>
                            <strong>{{ wood_type.name_ar }}</strong>
                        </td>
                        <td>
                            {% if wood_type.name_fr %}
                                {{ wood_type.name_fr }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if wood_type.description %}
                                <small>{{ wood_type.description[:50] }}{% if wood_type.description|length > 50 %}...{% endif %}</small>
                            {% else %}
                                <span class="text-muted">لا يوجد وصف</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ wood_type.products|length }}</span>
                        </td>
                        <td>
                            {% if wood_type.is_active %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i>
                                    نشط
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-pause"></i>
                                    غير نشط
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ wood_type.created_at.strftime('%Y-%m-%d') if wood_type.created_at else 'غير محدد' }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" title="عرض"
                                        onclick="viewWoodType({{ wood_type.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" title="تعديل"
                                        onclick="editWoodType({{ wood_type.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-warning" title="تغيير الحالة"
                                        onclick="toggleWoodTypeStatus({{ wood_type.id }})">
                                    <i class="fas fa-power-off"></i>
                                </button>
                                {% if wood_type.products|length == 0 %}
                                <button type="button" class="btn btn-outline-danger" title="حذف"
                                        onclick="deleteWoodType({{ wood_type.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tree fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد أنواع خشب</h5>
            <p class="text-muted">ابدأ بإضافة أنواع الخشب المختلفة</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addWoodTypeModal">
                <i class="fas fa-plus"></i>
                إضافة نوع جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة إضافة نوع خشب جديد -->
<div class="modal fade" id="addWoodTypeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة نوع خشب جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addWoodTypeForm" method="POST" action="/wood-types/add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name_ar" class="form-label">الاسم بالعربية *</label>
                        <input type="text" class="form-control" id="name_ar" name="name_ar" required
                               placeholder="مثال: خشب زان">
                    </div>
                    
                    <div class="mb-3">
                        <label for="name_fr" class="form-label">الاسم بالفرنسية</label>
                        <input type="text" class="form-control" id="name_fr" name="name_fr"
                               placeholder="Exemple: Bois de hêtre">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف مختصر عن نوع الخشب وخصائصه..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                نوع الخشب نشط
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ النوع
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
{% if wood_types %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie text-info me-2"></i>
                    الأنواع الأكثر استخداماً
                </h6>
            </div>
            <div class="card-body">
                {% set popular_types = wood_types|sort(attribute='products', reverse=true)[:5] %}
                {% if popular_types %}
                <ul class="list-unstyled mb-0">
                    {% for wood_type in popular_types %}
                    <li class="mb-2 d-flex justify-content-between align-items-center">
                        <span>{{ wood_type.name_ar }}</span>
                        <span class="badge bg-primary">{{ wood_type.products|length }} منتج</span>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-muted mb-0">لا توجد بيانات كافية</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle text-success me-2"></i>
                    معلومات مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-2">
                    <small><strong>نصيحة:</strong> أضف الاسم بالفرنسية لدعم العملاء الناطقين بالفرنسية</small>
                </div>
                <div class="alert alert-warning mb-2">
                    <small><strong>تنبيه:</strong> لا يمكن حذف نوع خشب مرتبط بمنتجات</small>
                </div>
                <div class="alert alert-success mb-0">
                    <small><strong>ملاحظة:</strong> يمكن إيقاف نوع الخشب بدلاً من حذفه</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function viewWoodType(id) {
    alert('سيتم تطوير صفحة عرض نوع الخشب قريباً - النوع رقم: ' + id);
}

function editWoodType(id) {
    alert('سيتم تطوير صفحة تعديل نوع الخشب قريباً - النوع رقم: ' + id);
}

function toggleWoodTypeStatus(id) {
    if (confirm('هل أنت متأكد من تغيير حالة هذا النوع؟')) {
        alert('سيتم تطوير وظيفة تغيير الحالة قريباً - النوع رقم: ' + id);
    }
}

function deleteWoodType(id) {
    if (confirm('هل أنت متأكد من حذف هذا النوع؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        alert('سيتم تطوير وظيفة الحذف قريباً - النوع رقم: ' + id);
    }
}

// التحقق من صحة النموذج
document.getElementById('addWoodTypeForm').addEventListener('submit', function(e) {
    const nameAr = document.getElementById('name_ar').value.trim();
    
    if (!nameAr) {
        e.preventDefault();
        alert('يرجى إدخال اسم النوع بالعربية');
        return false;
    }
});
</script>
{% endblock %}
