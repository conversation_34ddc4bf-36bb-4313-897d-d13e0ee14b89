#!/usr/bin/env python3
"""
إعداد سريع لنظام إدارة المخازن
يحل مشاكل التوافق ويثبت المتطلبات بالطريقة الصحيحة
"""

import subprocess
import sys
import os
from pathlib import Path

def print_step(step_num, description):
    """طباعة خطوة الإعداد"""
    print(f"\n{step_num}️⃣ {description}")
    print("-" * 40)

def run_pip_command(command, description):
    """تشغيل أمر pip مع معالجة الأخطاء"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip"] + command.split()[1:], 
            check=True, 
            capture_output=True, 
            text=True
        )
        print(f"✅ {description} - نجح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - فشل")
        if e.stderr:
            print(f"الخطأ: {e.stderr}")
        return False

def main():
    """الدالة الرئيسية للإعداد السريع"""
    print("🚀 الإعداد السريع لنظام إدارة المخازن والمحاسبة")
    print("=" * 60)
    
    # التحقق من Python
    print_step(1, "فحص إصدار Python")
    python_version = sys.version_info
    if python_version < (3, 8):
        print(f"❌ يتطلب Python 3.8+ (الحالي: {python_version.major}.{python_version.minor})")
        input("اضغط Enter للخروج...")
        return
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # تحديث pip
    print_step(2, "تحديث pip")
    run_pip_command("pip install --upgrade pip", "تحديث pip")
    
    # إلغاء تثبيت المكتبات المتعارضة
    print_step(3, "إزالة المكتبات المتعارضة")
    conflicting_packages = ["SQLAlchemy", "Flask-SQLAlchemy", "typing-extensions"]
    for package in conflicting_packages:
        run_pip_command(f"pip uninstall -y {package}", f"إزالة {package}")
    
    # تثبيت المكتبات بالترتيب الصحيح
    print_step(4, "تثبيت المكتبات الأساسية")
    essential_packages = [
        "typing-extensions==4.8.0",
        "SQLAlchemy==1.4.53", 
        "Werkzeug==2.3.7",
        "Flask==2.3.3",
        "Flask-SQLAlchemy==3.0.5",
        "Flask-Login==0.6.3",
        "Flask-WTF==1.1.1",
        "WTForms==3.0.1",
        "Jinja2==3.1.2"
    ]
    
    for package in essential_packages:
        if not run_pip_command(f"pip install {package}", f"تثبيت {package}"):
            print(f"⚠️ فشل في تثبيت {package}")
    
    # تثبيت المكتبات الإضافية
    print_step(5, "تثبيت المكتبات الإضافية")
    additional_packages = [
        "openpyxl==3.1.2",
        "reportlab==4.0.4", 
        "python-dateutil==2.8.2",
        "Babel==2.12.1",
        "Flask-Babel==4.0.0"
    ]
    
    for package in additional_packages:
        run_pip_command(f"pip install {package}", f"تثبيت {package}")
    
    # إنشاء المجلدات المطلوبة
    print_step(6, "إنشاء المجلدات")
    directories = ["uploads", "backups", "logs", "static/css", "static/js", "static/images"]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ مجلد: {directory}")
    
    # اختبار الاستيراد
    print_step(7, "اختبار التثبيت")
    try:
        import flask
        import sqlalchemy
        import flask_sqlalchemy
        import flask_login
        print("✅ جميع المكتبات الأساسية متوفرة")
        
        # اختبار استيراد التطبيق
        sys.path.insert(0, '.')
        try:
            import app_simple
            print("✅ تم استيراد التطبيق بنجاح")
        except Exception as e:
            print(f"⚠️ مشكلة في استيراد التطبيق: {e}")
            
    except ImportError as e:
        print(f"❌ مشكلة في المكتبات: {e}")
        input("اضغط Enter للخروج...")
        return
    
    # إنشاء ملف تشغيل مبسط
    print_step(8, "إنشاء ملف التشغيل")
    simple_start_content = '''@echo off
echo Starting Warehouse Management System...
python app_simple.py
pause
'''
    
    with open("run.bat", "w", encoding="utf-8") as f:
        f.write(simple_start_content)
    print("✅ تم إنشاء ملف run.bat")
    
    # رسالة النجاح
    print("\n" + "=" * 60)
    print("🎉 تم الإعداد بنجاح!")
    print("=" * 60)
    print("\n📋 لتشغيل النظام:")
    print("1. انقر مرتين على run.bat")
    print("2. أو اكتب: python app_simple.py")
    print("3. افتح المتصفح على: http://localhost:5000")
    print("\n🔑 بيانات الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    
    print("\n💡 في حالة وجود مشاكل:")
    print("   شغّل: python fix_dependencies.py")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
