#!/usr/bin/env python3
"""
ملف اختبار النظام
يقوم بفحص جميع الوظائف الأساسية للنظام
"""

import requests
import json
import time
from datetime import datetime

class WarehouseSystemTester:
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """تسجيل نتيجة الاختبار"""
        status = "✅ نجح" if success else "❌ فشل"
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        print(f"{status} - {test_name}: {message}")
        
    def test_server_connection(self):
        """اختبار الاتصال بالخادم"""
        try:
            response = self.session.get(self.base_url, timeout=5)
            if response.status_code == 200:
                self.log_test("اتصال الخادم", True, "الخادم يعمل بشكل طبيعي")
                return True
            else:
                self.log_test("اتصال الخادم", False, f"رمز الاستجابة: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("اتصال الخادم", False, f"خطأ في الاتصال: {str(e)}")
            return False
    
    def test_login_page(self):
        """اختبار صفحة تسجيل الدخول"""
        try:
            response = self.session.get(f"{self.base_url}/login")
            if response.status_code == 200 and "تسجيل الدخول" in response.text:
                self.log_test("صفحة تسجيل الدخول", True, "الصفحة تحمل بشكل صحيح")
                return True
            else:
                self.log_test("صفحة تسجيل الدخول", False, "الصفحة لا تحمل بشكل صحيح")
                return False
        except Exception as e:
            self.log_test("صفحة تسجيل الدخول", False, f"خطأ: {str(e)}")
            return False
    
    def test_admin_login(self):
        """اختبار تسجيل دخول المدير"""
        try:
            # الحصول على صفحة تسجيل الدخول أولاً
            login_page = self.session.get(f"{self.base_url}/login")
            
            # تسجيل الدخول
            login_data = {
                'username': 'admin',
                'password': 'admin123'
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            
            # التحقق من إعادة التوجيه للوحة التحكم
            if response.status_code == 302 or "لوحة التحكم" in response.text:
                self.log_test("تسجيل دخول المدير", True, "تم تسجيل الدخول بنجاح")
                return True
            else:
                self.log_test("تسجيل دخول المدير", False, "فشل في تسجيل الدخول")
                return False
        except Exception as e:
            self.log_test("تسجيل دخول المدير", False, f"خطأ: {str(e)}")
            return False
    
    def test_dashboard_access(self):
        """اختبار الوصول للوحة التحكم"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200 and "لوحة التحكم" in response.text:
                self.log_test("لوحة التحكم", True, "يمكن الوصول للوحة التحكم")
                return True
            else:
                self.log_test("لوحة التحكم", False, "لا يمكن الوصول للوحة التحكم")
                return False
        except Exception as e:
            self.log_test("لوحة التحكم", False, f"خطأ: {str(e)}")
            return False
    
    def test_products_page(self):
        """اختبار صفحة المنتجات"""
        try:
            response = self.session.get(f"{self.base_url}/products")
            if response.status_code == 200 and "إدارة المنتجات" in response.text:
                self.log_test("صفحة المنتجات", True, "صفحة المنتجات تعمل")
                return True
            else:
                self.log_test("صفحة المنتجات", False, "صفحة المنتجات لا تعمل")
                return False
        except Exception as e:
            self.log_test("صفحة المنتجات", False, f"خطأ: {str(e)}")
            return False
    
    def test_customers_page(self):
        """اختبار صفحة العملاء"""
        try:
            response = self.session.get(f"{self.base_url}/customers")
            if response.status_code == 200 and "إدارة العملاء" in response.text:
                self.log_test("صفحة العملاء", True, "صفحة العملاء تعمل")
                return True
            else:
                self.log_test("صفحة العملاء", False, "صفحة العملاء لا تعمل")
                return False
        except Exception as e:
            self.log_test("صفحة العملاء", False, f"خطأ: {str(e)}")
            return False
    
    def test_invoices_page(self):
        """اختبار صفحة الفواتير"""
        try:
            response = self.session.get(f"{self.base_url}/sales-invoices")
            if response.status_code == 200 and "فواتير المبيعات" in response.text:
                self.log_test("صفحة الفواتير", True, "صفحة الفواتير تعمل")
                return True
            else:
                self.log_test("صفحة الفواتير", False, "صفحة الفواتير لا تعمل")
                return False
        except Exception as e:
            self.log_test("صفحة الفواتير", False, f"خطأ: {str(e)}")
            return False
    
    def test_reports_page(self):
        """اختبار صفحة التقارير"""
        try:
            response = self.session.get(f"{self.base_url}/reports")
            if response.status_code == 200 and "التقارير والإحصائيات" in response.text:
                self.log_test("صفحة التقارير", True, "صفحة التقارير تعمل")
                return True
            else:
                self.log_test("صفحة التقارير", False, "صفحة التقارير لا تعمل")
                return False
        except Exception as e:
            self.log_test("صفحة التقارير", False, f"خطأ: {str(e)}")
            return False
    
    def test_wood_types_page(self):
        """اختبار صفحة أنواع الخشب"""
        try:
            response = self.session.get(f"{self.base_url}/wood-types")
            if response.status_code == 200 and "إدارة أنواع الخشب" in response.text:
                self.log_test("صفحة أنواع الخشب", True, "صفحة أنواع الخشب تعمل")
                return True
            else:
                self.log_test("صفحة أنواع الخشب", False, "صفحة أنواع الخشب لا تعمل")
                return False
        except Exception as e:
            self.log_test("صفحة أنواع الخشب", False, f"خطأ: {str(e)}")
            return False
    
    def test_users_page(self):
        """اختبار صفحة المستخدمين"""
        try:
            response = self.session.get(f"{self.base_url}/users")
            if response.status_code == 200 and "إدارة المستخدمين" in response.text:
                self.log_test("صفحة المستخدمين", True, "صفحة المستخدمين تعمل")
                return True
            else:
                self.log_test("صفحة المستخدمين", False, "صفحة المستخدمين لا تعمل")
                return False
        except Exception as e:
            self.log_test("صفحة المستخدمين", False, f"خطأ: {str(e)}")
            return False
    
    def test_language_switching(self):
        """اختبار تبديل اللغة"""
        try:
            # تبديل للفرنسية
            response = self.session.get(f"{self.base_url}/set_language/fr")
            if response.status_code == 302:
                self.log_test("تبديل اللغة", True, "تبديل اللغة يعمل")
                return True
            else:
                self.log_test("تبديل اللغة", False, "تبديل اللغة لا يعمل")
                return False
        except Exception as e:
            self.log_test("تبديل اللغة", False, f"خطأ: {str(e)}")
            return False
    
    def test_responsive_design(self):
        """اختبار التصميم المتجاوب"""
        try:
            # محاكاة جهاز موبايل
            headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            }
            response = self.session.get(f"{self.base_url}/", headers=headers)
            
            if response.status_code == 200 and "viewport" in response.text:
                self.log_test("التصميم المتجاوب", True, "التصميم يدعم الأجهزة المحمولة")
                return True
            else:
                self.log_test("التصميم المتجاوب", False, "التصميم قد لا يدعم الأجهزة المحمولة")
                return False
        except Exception as e:
            self.log_test("التصميم المتجاوب", False, f"خطأ: {str(e)}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار النظام...")
        print("=" * 50)
        
        # اختبارات الاتصال الأساسية
        if not self.test_server_connection():
            print("❌ فشل في الاتصال بالخادم. تأكد من تشغيل النظام.")
            return
        
        # اختبارات الصفحات
        self.test_login_page()
        self.test_admin_login()
        self.test_dashboard_access()
        self.test_products_page()
        self.test_customers_page()
        self.test_invoices_page()
        self.test_reports_page()
        self.test_wood_types_page()
        self.test_users_page()
        
        # اختبارات الوظائف
        self.test_language_switching()
        self.test_responsive_design()
        
        # عرض النتائج
        self.show_results()
    
    def show_results(self):
        """عرض نتائج الاختبارات"""
        print("\n" + "=" * 50)
        print("📊 نتائج الاختبارات")
        print("=" * 50)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        percentage = (passed / total) * 100 if total > 0 else 0
        
        print(f"✅ اختبارات نجحت: {passed}")
        print(f"❌ اختبارات فشلت: {total - passed}")
        print(f"📈 نسبة النجاح: {percentage:.1f}%")
        
        if percentage >= 90:
            print("🎉 النظام يعمل بشكل ممتاز!")
        elif percentage >= 70:
            print("👍 النظام يعمل بشكل جيد مع بعض المشاكل البسيطة")
        else:
            print("⚠️ النظام يحتاج إلى مراجعة وإصلاح")
        
        # عرض الاختبارات الفاشلة
        failed_tests = [result for result in self.test_results if not result['success']]
        if failed_tests:
            print("\n❌ الاختبارات الفاشلة:")
            for test in failed_tests:
                print(f"  - {test['test']}: {test['message']}")
        
        print("\n" + "=" * 50)

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة اختبار نظام إدارة المخازن والمحاسبة")
    print("تأكد من تشغيل النظام على http://localhost:5000 قبل بدء الاختبار")
    
    input("اضغط Enter للمتابعة...")
    
    tester = WarehouseSystemTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
