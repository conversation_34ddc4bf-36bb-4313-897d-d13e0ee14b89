from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///warehouse_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'

# نموذج المستخدمين
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    role = db.Column(db.String(50), nullable=False, default='user')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<User {self.username}>'

# نموذج أنواع الخشب
class WoodType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100), nullable=True)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# نموذج المنتجات
class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    code = db.Column(db.String(50), unique=True, nullable=False)
    wood_type_id = db.Column(db.Integer, db.ForeignKey('wood_type.id'), nullable=False)
    
    length = db.Column(db.Float)
    width = db.Column(db.Float)
    thickness = db.Column(db.Float)
    
    quantity = db.Column(db.Float, default=0)
    unit = db.Column(db.String(20), default='متر مكعب')
    
    cost_price = db.Column(db.Float, default=0)
    selling_price = db.Column(db.Float, default=0)
    min_quantity = db.Column(db.Float, default=0)
    
    quality_grade = db.Column(db.String(50))
    location = db.Column(db.String(100))
    notes = db.Column(db.Text)
    
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    wood_type = db.relationship('WoodType', backref='products')
    
    def __repr__(self):
        return f'<Product {self.name}>'

# نموذج العملاء
class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    contact_person = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    
    balance = db.Column(db.Float, default=0)
    credit_limit = db.Column(db.Float, default=0)
    
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
@login_required
def index():
    # إحصائيات بسيطة
    total_products = Product.query.filter_by(is_active=True).count()
    active_customers = Customer.query.filter_by(is_active=True).count()
    today_sales = 0  # سيتم تطويرها لاحقاً
    low_stock_products = Product.query.filter(
        Product.quantity <= Product.min_quantity,
        Product.is_active == True
    ).limit(5).all()
    
    return render_template('dashboard.html',
                         total_products=total_products,
                         today_sales=today_sales,
                         active_customers=active_customers,
                         low_stock_alerts=len(low_stock_products),
                         low_stock_products=low_stock_products,
                         recent_sales=[])

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

# تغيير اللغة
@app.route('/set_language/<language>')
def set_language(language=None):
    session['language'] = language
    return redirect(request.referrer or url_for('index'))

# صفحة المنتجات
@app.route('/products')
@login_required
def products():
    products = Product.query.filter_by(is_active=True).all()
    return render_template('products/list.html', products=products)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='admin',
                password_hash=generate_password_hash('admin123')
            )
            db.session.add(admin_user)
            
            # إضافة بعض أنواع الخشب
            wood_types = [
                WoodType(name_ar='خشب زان', name_fr='Bois de hêtre', description='خشب صلب عالي الجودة'),
                WoodType(name_ar='خشب سنديان', name_fr='Bois de chêne', description='خشب قوي ومتين'),
                WoodType(name_ar='MDF', name_fr='MDF', description='خشب مضغوط متوسط الكثافة')
            ]
            
            for wood_type in wood_types:
                db.session.add(wood_type)
            
            db.session.commit()
            
            # إضافة بعض المنتجات التجريبية
            if WoodType.query.first():
                sample_products = [
                    Product(
                        name='لوح خشب زان 2×4',
                        code='PRD-BEECH-001',
                        wood_type_id=1,
                        length=2.0,
                        width=4.0,
                        thickness=0.05,
                        quantity=100.0,
                        unit='قطعة',
                        cost_price=25.0,
                        selling_price=35.0,
                        min_quantity=20.0,
                        quality_grade='ممتاز',
                        location='المخزن الرئيسي - رف A1'
                    ),
                    Product(
                        name='لوح MDF 2×3',
                        code='PRD-MDF-001',
                        wood_type_id=3,
                        length=2.0,
                        width=3.0,
                        thickness=0.018,
                        quantity=5.0,  # كمية قليلة لاختبار التنبيه
                        unit='قطعة',
                        cost_price=15.0,
                        selling_price=22.0,
                        min_quantity=50.0,
                        quality_grade='تجاري',
                        location='المخزن الثانوي - رف C1'
                    )
                ]
                
                for product in sample_products:
                    db.session.add(product)
                
                # إضافة عميل تجريبي
                customer = Customer(
                    name='شركة الأثاث الحديث',
                    contact_person='أحمد محمد',
                    phone='0501234567',
                    email='<EMAIL>',
                    address='الرياض، حي الملز',
                    credit_limit=50000.0
                )
                db.session.add(customer)
                
                db.session.commit()
    
    print("تم تشغيل النظام بنجاح!")
    print("يمكنك الوصول إلى النظام على: http://localhost:5000")
    print("المستخدم: admin | كلمة المرور: admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
