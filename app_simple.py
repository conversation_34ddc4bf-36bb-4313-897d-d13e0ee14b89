from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///warehouse_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'

# نموذج المستخدمين
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    role = db.Column(db.String(50), nullable=False, default='user')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<User {self.username}>'

# نموذج أنواع الخشب
class WoodType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(100), nullable=False)
    name_fr = db.Column(db.String(100), nullable=True)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# نموذج المنتجات
class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    code = db.Column(db.String(50), unique=True, nullable=False)
    wood_type_id = db.Column(db.Integer, db.ForeignKey('wood_type.id'), nullable=False)
    
    length = db.Column(db.Float)
    width = db.Column(db.Float)
    thickness = db.Column(db.Float)
    
    quantity = db.Column(db.Float, default=0)
    unit = db.Column(db.String(20), default='متر مكعب')
    
    cost_price = db.Column(db.Float, default=0)
    selling_price = db.Column(db.Float, default=0)
    min_quantity = db.Column(db.Float, default=0)
    
    quality_grade = db.Column(db.String(50))
    location = db.Column(db.String(100))
    notes = db.Column(db.Text)
    
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    wood_type = db.relationship('WoodType', backref='products')
    
    def __repr__(self):
        return f'<Product {self.name}>'

# نموذج العملاء
class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    contact_person = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)

    balance = db.Column(db.Float, default=0)
    credit_limit = db.Column(db.Float, default=0)

    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# نموذج فواتير المبيعات
class SalesInvoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)

    invoice_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    due_date = db.Column(db.Date)

    subtotal = db.Column(db.Float, default=0)
    tax_rate = db.Column(db.Float, default=0)
    tax_amount = db.Column(db.Float, default=0)
    discount_rate = db.Column(db.Float, default=0)
    discount_amount = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float, default=0)
    paid_amount = db.Column(db.Float, default=0)

    status = db.Column(db.String(20), default='draft')  # draft, confirmed, cancelled
    payment_status = db.Column(db.String(20), default='unpaid')  # paid, partial, unpaid

    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    customer = db.relationship('Customer', backref='sales_invoices')
    creator = db.relationship('User', backref='created_invoices')

# نموذج عناصر فاتورة المبيعات
class SalesInvoiceItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('sales_invoice.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)

    quantity = db.Column(db.Float, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)

    # العلاقات
    invoice = db.relationship('SalesInvoice', backref='items')
    product = db.relationship('Product', backref='sales_items')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
@login_required
def index():
    # إحصائيات بسيطة
    total_products = Product.query.filter_by(is_active=True).count()
    active_customers = Customer.query.filter_by(is_active=True).count()
    today_sales = 0  # سيتم تطويرها لاحقاً
    low_stock_products = Product.query.filter(
        Product.quantity <= Product.min_quantity,
        Product.is_active == True
    ).limit(5).all()
    
    return render_template('dashboard.html',
                         total_products=total_products,
                         today_sales=today_sales,
                         active_customers=active_customers,
                         low_stock_alerts=len(low_stock_products),
                         low_stock_products=low_stock_products,
                         recent_sales=[])

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

# تغيير اللغة
@app.route('/set_language/<language>')
def set_language(language=None):
    session['language'] = language
    return redirect(request.referrer or url_for('index'))

# صفحة المنتجات
@app.route('/products')
@login_required
def products():
    products = Product.query.filter_by(is_active=True).all()
    return render_template('products/list.html', products=products)

# صفحة إضافة منتج جديد
@app.route('/products/add', methods=['GET', 'POST'])
@login_required
def add_product():
    if request.method == 'GET':
        wood_types = WoodType.query.filter_by(is_active=True).all()
        return render_template('products/add.html', wood_types=wood_types)

    try:
        # استلام البيانات من النموذج
        name = request.form.get('name')
        code = request.form.get('code')
        wood_type_id = request.form.get('wood_type_id')
        quality_grade = request.form.get('quality_grade')

        # المقاسات
        length = float(request.form.get('length') or 0)
        width = float(request.form.get('width') or 0)
        thickness = float(request.form.get('thickness') or 0)

        # الكمية
        quantity = float(request.form.get('quantity') or 0)
        unit = request.form.get('unit')
        min_quantity = float(request.form.get('min_quantity') or 0)

        # التسعير
        cost_price = float(request.form.get('cost_price') or 0)
        selling_price = float(request.form.get('selling_price') or 0)

        # معلومات إضافية
        location = request.form.get('location')
        notes = request.form.get('notes')
        is_active = 'is_active' in request.form

        # التحقق من صحة البيانات
        if not all([name, code, wood_type_id, unit]):
            flash('جميع الحقول المطلوبة يجب ملؤها', 'error')
            return redirect(url_for('add_product'))

        if quantity < 0 or min_quantity < 0 or selling_price <= 0:
            flash('يرجى إدخال قيم صحيحة للكميات والأسعار', 'error')
            return redirect(url_for('add_product'))

        # التحقق من عدم وجود كود مكرر
        if Product.query.filter_by(code=code).first():
            flash('كود المنتج موجود بالفعل', 'error')
            return redirect(url_for('add_product'))

        # إنشاء المنتج الجديد
        new_product = Product(
            name=name,
            code=code,
            wood_type_id=int(wood_type_id),
            quality_grade=quality_grade,
            length=length if length > 0 else None,
            width=width if width > 0 else None,
            thickness=thickness if thickness > 0 else None,
            quantity=quantity,
            unit=unit,
            min_quantity=min_quantity,
            cost_price=cost_price,
            selling_price=selling_price,
            location=location,
            notes=notes,
            is_active=is_active
        )

        db.session.add(new_product)
        db.session.commit()

        flash(f'تم إضافة المنتج {name} بنجاح', 'success')
        return redirect(url_for('products'))

    except ValueError as e:
        flash('يرجى إدخال قيم رقمية صحيحة', 'error')
        return redirect(url_for('add_product'))
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة المنتج: {str(e)}', 'error')
        return redirect(url_for('add_product'))

# صفحة المستخدمين (للمديرين فقط)
@app.route('/users')
@login_required
def users():
    if current_user.role not in ['admin', 'manager']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))

    users = User.query.all()
    return render_template('users/list.html', users=users)

# إضافة مستخدم جديد
@app.route('/users/add', methods=['POST'])
@login_required
def add_user():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة مستخدمين', 'error')
        return redirect(url_for('users'))

    try:
        # التحقق من البيانات
        full_name = request.form.get('full_name')
        username = request.form.get('username')
        email = request.form.get('email')
        role = request.form.get('role')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        is_active = 'is_active' in request.form

        # التحقق من صحة البيانات
        if not all([full_name, username, email, role, password]):
            flash('جميع الحقول مطلوبة', 'error')
            return redirect(url_for('users'))

        if password != confirm_password:
            flash('كلمات المرور غير متطابقة', 'error')
            return redirect(url_for('users'))

        # التحقق من عدم وجود المستخدم
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'error')
            return redirect(url_for('users'))

        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'error')
            return redirect(url_for('users'))

        # إنشاء المستخدم الجديد
        new_user = User(
            full_name=full_name,
            username=username,
            email=email,
            role=role,
            password_hash=generate_password_hash(password),
            is_active=is_active
        )

        db.session.add(new_user)
        db.session.commit()

        flash(f'تم إنشاء المستخدم {full_name} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء المستخدم: {str(e)}', 'error')

    return redirect(url_for('users'))

# صفحة أنواع الخشب
@app.route('/wood-types')
@login_required
def wood_types():
    wood_types = WoodType.query.all()
    products_count = Product.query.count()
    return render_template('wood_types/list.html', wood_types=wood_types, products_count=products_count)

# إضافة نوع خشب جديد
@app.route('/wood-types/add', methods=['POST'])
@login_required
def add_wood_type():
    if current_user.role not in ['admin', 'manager']:
        flash('ليس لديك صلاحية لإضافة أنواع خشب', 'error')
        return redirect(url_for('wood_types'))

    try:
        name_ar = request.form.get('name_ar')
        name_fr = request.form.get('name_fr')
        description = request.form.get('description')
        is_active = 'is_active' in request.form

        if not name_ar:
            flash('اسم النوع بالعربية مطلوب', 'error')
            return redirect(url_for('wood_types'))

        # التحقق من عدم وجود نوع بنفس الاسم
        if WoodType.query.filter_by(name_ar=name_ar).first():
            flash('نوع الخشب موجود بالفعل', 'error')
            return redirect(url_for('wood_types'))

        new_wood_type = WoodType(
            name_ar=name_ar,
            name_fr=name_fr if name_fr else None,
            description=description if description else None,
            is_active=is_active
        )

        db.session.add(new_wood_type)
        db.session.commit()

        flash(f'تم إضافة نوع الخشب {name_ar} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة نوع الخشب: {str(e)}', 'error')

    return redirect(url_for('wood_types'))

# صفحة فواتير المبيعات
@app.route('/sales-invoices')
@login_required
def sales_invoices():
    status_filter = request.args.get('status')
    query = SalesInvoice.query

    if status_filter:
        query = query.filter_by(status=status_filter)

    invoices = query.order_by(SalesInvoice.created_at.desc()).all()
    return render_template('invoices/sales_list.html', invoices=invoices)

# صفحة إضافة فاتورة مبيعات جديدة
@app.route('/sales-invoices/add')
@login_required
def add_sales_invoice():
    customers = Customer.query.filter_by(is_active=True).all()
    products = Product.query.filter_by(is_active=True).all()
    return render_template('invoices/sales_add.html', customers=customers, products=products)

# حفظ فاتورة مبيعات جديدة
@app.route('/sales-invoices/save', methods=['POST'])
@login_required
def save_sales_invoice():
    try:
        # استلام البيانات الأساسية
        invoice_number = request.form.get('invoice_number')
        customer_id = request.form.get('customer_id')
        invoice_date = datetime.strptime(request.form.get('invoice_date'), '%Y-%m-%d').date()
        due_date_str = request.form.get('due_date')
        due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date() if due_date_str else None

        # الحسابات
        subtotal = float(request.form.get('subtotal', 0))
        tax_rate = float(request.form.get('tax_rate', 0))
        tax_amount = float(request.form.get('tax_amount', 0))
        discount_rate = float(request.form.get('discount_rate', 0))
        discount_amount = float(request.form.get('discount_amount', 0))
        total_amount = float(request.form.get('total_amount', 0))

        notes = request.form.get('notes')
        action = request.form.get('action', 'draft')  # draft أو confirm

        # التحقق من البيانات
        if not all([invoice_number, customer_id]):
            flash('يرجى ملء جميع الحقول المطلوبة', 'error')
            return redirect(url_for('add_sales_invoice'))

        # التحقق من عدم وجود رقم فاتورة مكرر
        if SalesInvoice.query.filter_by(invoice_number=invoice_number).first():
            flash('رقم الفاتورة موجود بالفعل', 'error')
            return redirect(url_for('add_sales_invoice'))

        # إنشاء الفاتورة
        invoice = SalesInvoice(
            invoice_number=invoice_number,
            customer_id=int(customer_id),
            invoice_date=invoice_date,
            due_date=due_date,
            subtotal=subtotal,
            tax_rate=tax_rate,
            tax_amount=tax_amount,
            discount_rate=discount_rate,
            discount_amount=discount_amount,
            total_amount=total_amount,
            notes=notes,
            status='confirmed' if action == 'confirm' else 'draft',
            created_by=current_user.id
        )

        db.session.add(invoice)
        db.session.flush()  # للحصول على معرف الفاتورة

        # إضافة عناصر الفاتورة
        product_ids = request.form.getlist('product_id[]')
        quantities = request.form.getlist('quantity[]')
        unit_prices = request.form.getlist('unit_price[]')
        total_prices = request.form.getlist('total_price[]')

        for i in range(len(product_ids)):
            if product_ids[i]:  # تجاهل العناصر الفارغة
                item = SalesInvoiceItem(
                    invoice_id=invoice.id,
                    product_id=int(product_ids[i]),
                    quantity=float(quantities[i]),
                    unit_price=float(unit_prices[i]),
                    total_price=float(total_prices[i])
                )
                db.session.add(item)

                # تحديث المخزون إذا كانت الفاتورة مؤكدة
                if action == 'confirm':
                    product = Product.query.get(int(product_ids[i]))
                    if product:
                        product.quantity -= float(quantities[i])

        db.session.commit()

        status_text = 'وتم تأكيدها' if action == 'confirm' else 'كمسودة'
        flash(f'تم حفظ الفاتورة {invoice_number} بنجاح {status_text}', 'success')
        return redirect(url_for('sales_invoices'))

    except ValueError as e:
        flash('يرجى إدخال قيم صحيحة', 'error')
        return redirect(url_for('add_sales_invoice'))
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حفظ الفاتورة: {str(e)}', 'error')
        return redirect(url_for('add_sales_invoice'))

# صفحة العملاء
@app.route('/customers')
@login_required
def customers():
    customers = Customer.query.all()
    return render_template('customers/list.html', customers=customers)

# إضافة عميل جديد
@app.route('/customers/add', methods=['POST'])
@login_required
def add_customer():
    try:
        name = request.form.get('name')
        contact_person = request.form.get('contact_person')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')
        credit_limit = float(request.form.get('credit_limit', 0))
        is_active = 'is_active' in request.form

        if not name:
            flash('اسم العميل مطلوب', 'error')
            return redirect(url_for('customers'))

        # التحقق من عدم وجود عميل بنفس الاسم
        if Customer.query.filter_by(name=name).first():
            flash('العميل موجود بالفعل', 'error')
            return redirect(url_for('customers'))

        new_customer = Customer(
            name=name,
            contact_person=contact_person if contact_person else None,
            phone=phone if phone else None,
            email=email if email else None,
            address=address if address else None,
            credit_limit=credit_limit,
            is_active=is_active
        )

        db.session.add(new_customer)
        db.session.commit()

        flash(f'تم إضافة العميل {name} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إضافة العميل: {str(e)}', 'error')

    return redirect(url_for('customers'))

# صفحة التقارير
@app.route('/reports')
@login_required
def reports():
    from sqlalchemy import func
    from datetime import datetime, timedelta

    # الحصول على فلاتر التاريخ
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    # تحديد التواريخ الافتراضية (الشهر الحالي)
    if not start_date_str:
        start_date = datetime.now().replace(day=1).date()
    else:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()

    if not end_date_str:
        end_date = datetime.now().date()
    else:
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

    # حساب الإحصائيات
    invoices = SalesInvoice.query.filter(
        SalesInvoice.invoice_date >= start_date,
        SalesInvoice.invoice_date <= end_date,
        SalesInvoice.status == 'confirmed'
    ).all()

    total_sales = sum(invoice.total_amount for invoice in invoices)
    total_cost = sum(
        sum(item.quantity * item.product.cost_price for item in invoice.items if item.product)
        for invoice in invoices
    )
    total_profit = total_sales - total_cost
    total_tax_collected = sum(invoice.tax_amount for invoice in invoices)
    total_discounts = sum(invoice.discount_amount for invoice in invoices)

    # قيمة المخزون
    products = Product.query.filter_by(is_active=True).all()
    inventory_value = sum(product.quantity * product.selling_price for product in products)

    # تقرير المخزون
    inventory_report = []
    for product in products:
        inventory_report.append({
            'name': product.name,
            'wood_type_name': product.wood_type.name_ar if product.wood_type else 'غير محدد',
            'quantity': product.quantity,
            'unit': product.unit,
            'min_quantity': product.min_quantity,
            'value': product.quantity * product.selling_price
        })

    # أفضل المنتجات (مبسط)
    top_products = []
    for product in products[:5]:
        sold_quantity = sum(
            item.quantity for item in product.sales_items
            if item.invoice.status == 'confirmed' and
            start_date <= item.invoice.invoice_date <= end_date
        )
        revenue = sum(
            item.total_price for item in product.sales_items
            if item.invoice.status == 'confirmed' and
            start_date <= item.invoice.invoice_date <= end_date
        )
        if sold_quantity > 0:
            top_products.append({
                'name': product.name,
                'sold_quantity': sold_quantity,
                'unit': product.unit,
                'revenue': revenue
            })

    # أفضل العملاء
    top_customers = []
    customers = Customer.query.filter_by(is_active=True).all()
    for customer in customers[:5]:
        customer_invoices = [inv for inv in customer.sales_invoices
                           if inv.status == 'confirmed' and
                           start_date <= inv.invoice_date <= end_date]
        if customer_invoices:
            top_customers.append({
                'name': customer.name,
                'invoice_count': len(customer_invoices),
                'total_purchases': sum(inv.total_amount for inv in customer_invoices)
            })

    # بيانات الرسوم البيانية (مبسطة)
    sales_chart_labels = []
    sales_chart_data = []
    wood_types_labels = []
    wood_types_data = []

    # رسم المبيعات اليومية للأسبوع الماضي
    for i in range(7):
        date = end_date - timedelta(days=6-i)
        sales_chart_labels.append(date.strftime('%m-%d'))
        daily_sales = sum(
            inv.total_amount for inv in invoices
            if inv.invoice_date == date
        )
        sales_chart_data.append(float(daily_sales))

    # رسم أنواع الخشب
    wood_types = WoodType.query.filter_by(is_active=True).all()
    for wood_type in wood_types:
        wood_types_labels.append(wood_type.name_ar)
        wood_type_sales = sum(
            item.total_price for item in SalesInvoiceItem.query.all()
            if item.product and item.product.wood_type_id == wood_type.id and
            item.invoice.status == 'confirmed' and
            start_date <= item.invoice.invoice_date <= end_date
        )
        wood_types_data.append(float(wood_type_sales))

    return render_template('reports/index.html',
                         total_sales=total_sales,
                         total_profit=total_profit,
                         total_invoices=len(invoices),
                         inventory_value=inventory_value,
                         total_tax_collected=total_tax_collected,
                         total_cost=total_cost,
                         total_discounts=total_discounts,
                         inventory_report=inventory_report,
                         top_products=top_products,
                         top_customers=top_customers,
                         sales_chart_labels=sales_chart_labels,
                         sales_chart_data=sales_chart_data,
                         wood_types_labels=wood_types_labels,
                         wood_types_data=wood_types_data)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='admin',
                password_hash=generate_password_hash('admin123')
            )
            db.session.add(admin_user)
            
            # إضافة بعض أنواع الخشب
            wood_types = [
                WoodType(name_ar='خشب زان', name_fr='Bois de hêtre', description='خشب صلب عالي الجودة'),
                WoodType(name_ar='خشب سنديان', name_fr='Bois de chêne', description='خشب قوي ومتين'),
                WoodType(name_ar='MDF', name_fr='MDF', description='خشب مضغوط متوسط الكثافة')
            ]
            
            for wood_type in wood_types:
                db.session.add(wood_type)
            
            db.session.commit()
            
            # إضافة بعض المنتجات التجريبية
            if WoodType.query.first():
                sample_products = [
                    Product(
                        name='لوح خشب زان 2×4',
                        code='PRD-BEECH-001',
                        wood_type_id=1,
                        length=2.0,
                        width=4.0,
                        thickness=0.05,
                        quantity=100.0,
                        unit='قطعة',
                        cost_price=25.0,
                        selling_price=35.0,
                        min_quantity=20.0,
                        quality_grade='ممتاز',
                        location='المخزن الرئيسي - رف A1'
                    ),
                    Product(
                        name='لوح MDF 2×3',
                        code='PRD-MDF-001',
                        wood_type_id=3,
                        length=2.0,
                        width=3.0,
                        thickness=0.018,
                        quantity=5.0,  # كمية قليلة لاختبار التنبيه
                        unit='قطعة',
                        cost_price=15.0,
                        selling_price=22.0,
                        min_quantity=50.0,
                        quality_grade='تجاري',
                        location='المخزن الثانوي - رف C1'
                    )
                ]
                
                for product in sample_products:
                    db.session.add(product)
                
                # إضافة عميل تجريبي
                customer = Customer(
                    name='شركة الأثاث الحديث',
                    contact_person='أحمد محمد',
                    phone='0501234567',
                    email='<EMAIL>',
                    address='الرياض، حي الملز',
                    credit_limit=50000.0
                )
                db.session.add(customer)
                
                db.session.commit()
    
    print("تم تشغيل النظام بنجاح!")
    print("يمكنك الوصول إلى النظام على: http://localhost:5000")
    print("المستخدم: admin | كلمة المرور: admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
