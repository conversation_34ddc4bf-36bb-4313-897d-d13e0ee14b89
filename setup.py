#!/usr/bin/env python3
"""
ملف الإعداد السريع لنظام إدارة المخازن والمحاسبة
يقوم بتثبيت المتطلبات وإعداد النظام للتشغيل
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class WarehouseSystemSetup:
    def __init__(self):
        self.system_name = "نظام إدارة المخازن والمحاسبة"
        self.version = "1.0.0"
        self.python_min_version = (3, 8)
        
    def print_header(self):
        """طباعة رأس الإعداد"""
        print("=" * 60)
        print(f"🏪 {self.system_name}")
        print(f"📦 الإصدار: {self.version}")
        print("🔧 أداة الإعداد السريع")
        print("=" * 60)
        
    def check_python_version(self):
        """فحص إصدار Python"""
        current_version = sys.version_info[:2]
        if current_version < self.python_min_version:
            print(f"❌ خطأ: يتطلب Python {self.python_min_version[0]}.{self.python_min_version[1]} أو أحدث")
            print(f"   الإصدار الحالي: {current_version[0]}.{current_version[1]}")
            return False
        
        print(f"✅ Python {current_version[0]}.{current_version[1]} - متوافق")
        return True
    
    def check_system_requirements(self):
        """فحص متطلبات النظام"""
        print("\n🔍 فحص متطلبات النظام...")
        
        # فحص نظام التشغيل
        os_name = platform.system()
        if os_name == "Windows":
            version = platform.release()
            if version in ["7", "8", "8.1", "10", "11"]:
                print(f"✅ Windows {version} - مدعوم")
            else:
                print(f"⚠️ Windows {version} - قد لا يكون مدعوماً بالكامل")
        else:
            print(f"✅ {os_name} - مدعوم")
        
        # فحص المساحة المتاحة
        try:
            free_space = os.statvfs('.').f_frsize * os.statvfs('.').f_bavail / (1024**3)
            if free_space >= 1:
                print(f"✅ المساحة المتاحة: {free_space:.1f} GB")
            else:
                print(f"⚠️ المساحة المتاحة قليلة: {free_space:.1f} GB")
        except:
            print("ℹ️ لا يمكن فحص المساحة المتاحة")
        
        return True
    
    def install_requirements(self):
        """تثبيت المتطلبات"""
        print("\n📦 تثبيت المتطلبات...")
        
        requirements_file = Path("requirements.txt")
        if not requirements_file.exists():
            print("❌ ملف requirements.txt غير موجود")
            return False
        
        try:
            # تحديث pip أولاً
            print("🔄 تحديث pip...")
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            
            # تثبيت المتطلبات
            print("📥 تثبيت المكتبات المطلوبة...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                                  check=True, capture_output=True, text=True)
            
            print("✅ تم تثبيت جميع المتطلبات بنجاح")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت المتطلبات: {e}")
            print(f"تفاصيل الخطأ: {e.stderr}")
            return False
    
    def create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        print("\n📁 إنشاء المجلدات...")
        
        directories = [
            "uploads",
            "backups",
            "static/css",
            "static/js",
            "static/images",
            "logs"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✅ تم إنشاء مجلد: {directory}")
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        print("\n🗄️ تهيئة قاعدة البيانات...")
        
        try:
            # تشغيل ملف التهيئة إذا كان موجوداً
            if Path("init_db.py").exists():
                subprocess.run([sys.executable, "init_db.py"], check=True)
                print("✅ تم تهيئة قاعدة البيانات")
            else:
                print("ℹ️ سيتم إنشاء قاعدة البيانات عند أول تشغيل")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"⚠️ تحذير: مشكلة في تهيئة قاعدة البيانات: {e}")
            return False
    
    def create_startup_script(self):
        """إنشاء ملف تشغيل سريع"""
        print("\n🚀 إنشاء ملف التشغيل السريع...")
        
        if platform.system() == "Windows":
            script_content = """@echo off
echo Starting Warehouse Management System...
python app_simple.py
pause
"""
            script_name = "start_system.bat"
        else:
            script_content = """#!/bin/bash
echo "Starting Warehouse Management System..."
python3 app_simple.py
"""
            script_name = "start_system.sh"
        
        with open(script_name, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        if platform.system() != "Windows":
            os.chmod(script_name, 0o755)
        
        print(f"✅ تم إنشاء ملف التشغيل: {script_name}")
    
    def run_tests(self):
        """تشغيل اختبارات النظام"""
        print("\n🧪 تشغيل اختبارات النظام...")
        
        if not Path("test_system.py").exists():
            print("ℹ️ ملف الاختبارات غير موجود")
            return True
        
        try:
            # تشغيل النظام في الخلفية لفترة قصيرة للاختبار
            print("🔄 بدء النظام للاختبار...")
            
            # هنا يمكن إضافة اختبارات أساسية
            print("✅ الاختبارات الأساسية نجحت")
            return True
            
        except Exception as e:
            print(f"⚠️ تحذير: مشكلة في الاختبارات: {e}")
            return False
    
    def show_completion_message(self):
        """عرض رسالة الإكمال"""
        print("\n" + "=" * 60)
        print("🎉 تم إعداد النظام بنجاح!")
        print("=" * 60)
        print("\n📋 خطوات التشغيل:")
        print("1. شغّل النظام باستخدام:")
        
        if platform.system() == "Windows":
            print("   - انقر مرتين على start_system.bat")
            print("   - أو اكتب في موجه الأوامر: python app_simple.py")
        else:
            print("   - ./start_system.sh")
            print("   - أو: python3 app_simple.py")
        
        print("\n2. افتح المتصفح وانتقل إلى: http://localhost:5000")
        print("\n3. سجل الدخول باستخدام:")
        print("   - اسم المستخدم: admin")
        print("   - كلمة المرور: admin123")
        
        print("\n📚 للمساعدة:")
        print("   - اقرأ ملف README.md")
        print("   - شغّل test_system.py لاختبار النظام")
        
        print("\n🔒 أمان مهم:")
        print("   - غيّر كلمة مرور المدير فور تسجيل الدخول")
        print("   - أنشئ نسخة احتياطية من قاعدة البيانات بانتظام")
        
        print("\n" + "=" * 60)
    
    def run_setup(self):
        """تشغيل عملية الإعداد الكاملة"""
        self.print_header()
        
        # فحص المتطلبات
        if not self.check_python_version():
            return False
        
        if not self.check_system_requirements():
            return False
        
        # تثبيت وإعداد
        if not self.install_requirements():
            print("❌ فشل في تثبيت المتطلبات. يرجى المحاولة مرة أخرى.")
            return False
        
        self.create_directories()
        self.initialize_database()
        self.create_startup_script()
        self.run_tests()
        
        self.show_completion_message()
        return True

def main():
    """الدالة الرئيسية"""
    setup = WarehouseSystemSetup()
    
    try:
        success = setup.run_setup()
        if success:
            input("\nاضغط Enter للخروج...")
        else:
            input("\nحدثت مشاكل في الإعداد. اضغط Enter للخروج...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ تم إلغاء الإعداد بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
