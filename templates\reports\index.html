{% extends "base.html" %}

{% block title %}التقارير والإحصائيات - نظام إدارة المخازن{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar me-2"></i>
        التقارير والإحصائيات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download"></i>
                تصدير التقارير
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                    <i class="fas fa-file-excel me-2"></i>Excel
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                    <i class="fas fa-file-pdf me-2"></i>PDF
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- فلاتر التقارير -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            فلاتر التقارير
        </h5>
    </div>
    <div class="card-body">
        <form id="reportFilters" method="GET">
            <div class="row">
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="start_date" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ request.args.get('start_date', '') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="end_date" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ request.args.get('end_date', '') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label for="report_type" class="form-label">نوع التقرير</label>
                        <select class="form-select" id="report_type" name="report_type">
                            <option value="all" {{ 'selected' if request.args.get('report_type') == 'all' else '' }}>جميع التقارير</option>
                            <option value="sales" {{ 'selected' if request.args.get('report_type') == 'sales' else '' }}>تقرير المبيعات</option>
                            <option value="inventory" {{ 'selected' if request.args.get('report_type') == 'inventory' else '' }}>تقرير المخزون</option>
                            <option value="customers" {{ 'selected' if request.args.get('report_type') == 'customers' else '' }}>تقرير العملاء</option>
                            <option value="profit" {{ 'selected' if request.args.get('report_type') == 'profit' else '' }}>تقرير الأرباح</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                                تطبيق الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "%.2f"|format(total_sales) }}</h4>
                        <p class="mb-0">إجمالي المبيعات (ر.س)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "%.2f"|format(total_profit) }}</h4>
                        <p class="mb-0">إجمالي الأرباح (ر.س)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_invoices }}</h4>
                        <p class="mb-0">عدد الفواتير</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "%.2f"|format(inventory_value) }}</h4>
                        <p class="mb-0">قيمة المخزون (ر.س)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    مبيعات الشهر الحالي
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" width="100%" height="50"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع المبيعات حسب نوع الخشب
                </h5>
            </div>
            <div class="card-body">
                <canvas id="woodTypesChart" width="100%" height="50"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- تقارير مفصلة -->
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    أفضل المنتجات مبيعاً
                </h5>
            </div>
            <div class="card-body">
                {% if top_products %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية المباعة</th>
                                <th>الإيرادات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in top_products %}
                            <tr>
                                <td>{{ product.name }}</td>
                                <td>{{ product.sold_quantity }} {{ product.unit }}</td>
                                <td>{{ "%.2f"|format(product.revenue) }} ر.س</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد بيانات مبيعات</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    أفضل العملاء
                </h5>
            </div>
            <div class="card-body">
                {% if top_customers %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>عدد الفواتير</th>
                                <th>إجمالي المشتريات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in top_customers %}
                            <tr>
                                <td>{{ customer.name }}</td>
                                <td>{{ customer.invoice_count }}</td>
                                <td>{{ "%.2f"|format(customer.total_purchases) }} ر.س</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد بيانات عملاء</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- تقرير المخزون -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-warehouse me-2"></i>
                    تقرير حالة المخزون
                </h5>
            </div>
            <div class="card-body">
                {% if inventory_report %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>المنتج</th>
                                <th>نوع الخشب</th>
                                <th>الكمية الحالية</th>
                                <th>الحد الأدنى</th>
                                <th>قيمة المخزون</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in inventory_report %}
                            <tr>
                                <td>{{ item.name }}</td>
                                <td>{{ item.wood_type_name }}</td>
                                <td>{{ item.quantity }} {{ item.unit }}</td>
                                <td>{{ item.min_quantity }} {{ item.unit }}</td>
                                <td>{{ "%.2f"|format(item.value) }} ر.س</td>
                                <td>
                                    {% if item.quantity <= item.min_quantity %}
                                        <span class="badge bg-danger">نقص مخزون</span>
                                    {% elif item.quantity <= (item.min_quantity * 1.5) %}
                                        <span class="badge bg-warning">تحذير</span>
                                    {% else %}
                                        <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد منتجات في المخزون</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- تقرير الأرباح والخسائر -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    تقرير الأرباح والخسائر
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">الإيرادات</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>إجمالي المبيعات:</td>
                                <td class="text-end">{{ "%.2f"|format(total_sales) }} ر.س</td>
                            </tr>
                            <tr>
                                <td>الضرائب المحصلة:</td>
                                <td class="text-end">{{ "%.2f"|format(total_tax_collected) }} ر.س</td>
                            </tr>
                            <tr class="table-success">
                                <td><strong>إجمالي الإيرادات:</strong></td>
                                <td class="text-end"><strong>{{ "%.2f"|format(total_sales + total_tax_collected) }} ر.س</strong></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">التكاليف</h6>
                        <table class="table table-sm">
                            <tr>
                                <td>تكلفة البضاعة المباعة:</td>
                                <td class="text-end">{{ "%.2f"|format(total_cost) }} ر.س</td>
                            </tr>
                            <tr>
                                <td>الخصومات الممنوحة:</td>
                                <td class="text-end">{{ "%.2f"|format(total_discounts) }} ر.س</td>
                            </tr>
                            <tr class="table-danger">
                                <td><strong>إجمالي التكاليف:</strong></td>
                                <td class="text-end"><strong>{{ "%.2f"|format(total_cost + total_discounts) }} ر.س</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-{{ 'success' if total_profit > 0 else 'danger' }} text-center">
                            <h4>
                                {% if total_profit > 0 %}
                                    <i class="fas fa-arrow-up me-2"></i>
                                    صافي الربح: {{ "%.2f"|format(total_profit) }} ر.س
                                {% else %}
                                    <i class="fas fa-arrow-down me-2"></i>
                                    صافي الخسارة: {{ "%.2f"|format(total_profit|abs) }} ر.س
                                {% endif %}
                            </h4>
                            <p class="mb-0">
                                هامش الربح: {{ "%.2f"|format((total_profit / total_sales * 100) if total_sales > 0 else 0) }}%
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للمبيعات
const salesCtx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(salesCtx, {
    type: 'line',
    data: {
        labels: {{ sales_chart_labels|tojson }},
        datasets: [{
            label: 'المبيعات اليومية',
            data: {{ sales_chart_data|tojson }},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// رسم بياني لأنواع الخشب
const woodTypesCtx = document.getElementById('woodTypesChart').getContext('2d');
const woodTypesChart = new Chart(woodTypesCtx, {
    type: 'doughnut',
    data: {
        labels: {{ wood_types_labels|tojson }},
        datasets: [{
            data: {{ wood_types_data|tojson }},
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            }
        }
    }
});

// تصدير التقارير
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.open('/reports/export?' + params.toString(), '_blank');
}

// تحديث التواريخ الافتراضية
document.addEventListener('DOMContentLoaded', function() {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (!startDate.value) {
        const firstDay = new Date();
        firstDay.setDate(1);
        startDate.value = firstDay.toISOString().split('T')[0];
    }
    
    if (!endDate.value) {
        const today = new Date();
        endDate.value = today.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
