{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام إدارة المخازن{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة العملاء
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                <i class="fas fa-user-plus"></i>
                إضافة عميل جديد
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات العملاء -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ customers|length }}</h4>
                        <p class="mb-0">إجمالي العملاء</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ customers|selectattr("is_active")|list|length }}</h4>
                        <p class="mb-0">عملاء نشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "%.2f"|format(customers|sum(attribute="balance")) }}</h4>
                        <p class="mb-0">إجمالي الأرصدة (ر.س)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ customers|selectattr("balance", "gt", 0)|list|length }}</h4>
                        <p class="mb-0">عملاء لديهم مديونية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول العملاء -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة العملاء
        </h5>
    </div>
    <div class="card-body">
        {% if customers %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>اسم العميل</th>
                        <th>الشخص المسؤول</th>
                        <th>الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>الرصيد</th>
                        <th>حد الائتمان</th>
                        <th>عدد الفواتير</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers %}
                    <tr>
                        <td>
                            <strong>{{ customer.name }}</strong>
                            {% if customer.address %}
                            <br><small class="text-muted">{{ customer.address[:50] }}{% if customer.address|length > 50 %}...{% endif %}</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.contact_person %}
                                {{ customer.contact_person }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.phone %}
                                <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ customer.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.email %}
                                <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ customer.email }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.balance > 0 %}
                                <span class="text-danger">{{ "%.2f"|format(customer.balance) }} ر.س</span>
                            {% elif customer.balance < 0 %}
                                <span class="text-success">{{ "%.2f"|format(customer.balance|abs) }} ر.س دائن</span>
                            {% else %}
                                <span class="text-muted">0.00 ر.س</span>
                            {% endif %}
                        </td>
                        <td>
                            {{ "%.2f"|format(customer.credit_limit) }} ر.س
                        </td>
                        <td>
                            <span class="badge bg-info">{{ customer.sales_invoices|length }}</span>
                        </td>
                        <td>
                            {% if customer.is_active %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i>
                                    نشط
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-pause"></i>
                                    غير نشط
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" title="عرض"
                                        onclick="viewCustomer({{ customer.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" title="تعديل"
                                        onclick="editCustomer({{ customer.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" title="الفواتير"
                                        onclick="viewCustomerInvoices({{ customer.id }})">
                                    <i class="fas fa-file-invoice"></i>
                                </button>
                                <button type="button" class="btn btn-outline-warning" title="تغيير الحالة"
                                        onclick="toggleCustomerStatus({{ customer.id }})">
                                    <i class="fas fa-power-off"></i>
                                </button>
                                {% if customer.sales_invoices|length == 0 %}
                                <button type="button" class="btn btn-outline-danger" title="حذف"
                                        onclick="deleteCustomer({{ customer.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد عملاء</h5>
            <p class="text-muted">ابدأ بإضافة عملاء جدد للنظام</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                <i class="fas fa-user-plus"></i>
                إضافة عميل جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة إضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة عميل جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCustomerForm" method="POST" action="/customers/add">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم العميل *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_person" class="form-label">الشخص المسؤول</label>
                                <input type="text" class="form-control" id="contact_person" name="contact_person">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="credit_limit" class="form-label">حد الائتمان (ر.س)</label>
                                <input type="number" class="form-control" id="credit_limit" name="credit_limit" 
                                       step="0.01" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        العميل نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ العميل
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
{% if customers %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie text-info me-2"></i>
                    العملاء الأكثر نشاطاً
                </h6>
            </div>
            <div class="card-body">
                {% set active_customers = customers|sort(attribute='sales_invoices', reverse=true)[:5] %}
                {% if active_customers %}
                <ul class="list-unstyled mb-0">
                    {% for customer in active_customers %}
                    <li class="mb-2 d-flex justify-content-between align-items-center">
                        <span>{{ customer.name }}</span>
                        <span class="badge bg-primary">{{ customer.sales_invoices|length }} فاتورة</span>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-muted mb-0">لا توجد بيانات كافية</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    عملاء يحتاجون متابعة
                </h6>
            </div>
            <div class="card-body">
                {% set debtors = customers|selectattr("balance", "gt", 0)|list %}
                {% if debtors %}
                <ul class="list-unstyled mb-0">
                    {% for customer in debtors[:3] %}
                    <li class="mb-2">
                        <strong>{{ customer.name }}</strong>
                        <br>
                        <small class="text-danger">
                            مديونية: {{ "%.2f"|format(customer.balance) }} ر.س
                        </small>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-success mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    جميع العملاء محدثين
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function viewCustomer(id) {
    window.location.href = '/customers/' + id;
}

function editCustomer(id) {
    window.location.href = '/customers/' + id + '/edit';
}

function viewCustomerInvoices(id) {
    window.location.href = '/sales-invoices?customer_id=' + id;
}

function toggleCustomerStatus(id) {
    if (confirm('هل أنت متأكد من تغيير حالة هذا العميل؟')) {
        alert('سيتم تطوير وظيفة تغيير الحالة قريباً - العميل رقم: ' + id);
    }
}

function deleteCustomer(id) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        alert('سيتم تطوير وظيفة الحذف قريباً - العميل رقم: ' + id);
    }
}

// التحقق من صحة النموذج
document.getElementById('addCustomerForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('يرجى إدخال اسم العميل');
        return false;
    }
});
</script>
{% endblock %}
