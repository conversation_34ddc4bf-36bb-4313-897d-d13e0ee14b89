#!/usr/bin/env python3
"""
نسخة مبسطة من نظام إدارة المخازن
تتجنب مشاكل التوافق في SQLAlchemy
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import sqlite3
import os
import json

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'warehouse-secret-key-2025'

# إضافة فلاتر مخصصة لـ Jinja2
@app.template_filter('safe_float')
def safe_float_filter(value, default=0.0):
    """فلتر لتحويل القيم إلى float بأمان"""
    return safe_float(value, default)

@app.template_filter('safe_int')
def safe_int_filter(value, default=0):
    """فلتر لتحويل القيم إلى int بأمان"""
    return safe_int(value, default)

# مسار قاعدة البيانات
DATABASE = 'warehouse_basic.db'

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def safe_float(value, default=0.0):
    """تحويل آمن للقيم إلى float"""
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """تحويل آمن للقيم إلى int"""
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def init_database():
    """تهيئة قاعدة البيانات"""
    conn = get_db_connection()
    
    # إنشاء جدول المستخدمين
    conn.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            full_name TEXT NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول أنواع الخشب
    conn.execute('''
        CREATE TABLE IF NOT EXISTS wood_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_ar TEXT NOT NULL,
            name_fr TEXT,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول المنتجات
    conn.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT UNIQUE NOT NULL,
            wood_type_id INTEGER,
            length REAL,
            width REAL,
            thickness REAL,
            quantity REAL DEFAULT 0,
            unit TEXT DEFAULT 'قطعة',
            cost_price REAL DEFAULT 0,
            selling_price REAL DEFAULT 0,
            min_quantity REAL DEFAULT 0,
            quality_grade TEXT,
            location TEXT,
            notes TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (wood_type_id) REFERENCES wood_types (id)
        )
    ''')
    
    # إنشاء جدول العملاء
    conn.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            balance REAL DEFAULT 0,
            credit_limit REAL DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء المستخدم الافتراضي
    admin_exists = conn.execute('SELECT id FROM users WHERE username = ?', ('admin',)).fetchone()
    if not admin_exists:
        conn.execute('''
            INSERT INTO users (username, email, full_name, password_hash, role)
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', 'المدير العام', 
              generate_password_hash('admin123'), 'admin'))
    
    # إضافة أنواع خشب افتراضية
    wood_types_data = [
        ('خشب زان', 'Bois de hêtre', 'خشب صلب عالي الجودة'),
        ('خشب سنديان', 'Bois de chêne', 'خشب قوي ومتين'),
        ('خشب صنوبر', 'Bois de pin', 'خشب لين سهل التشكيل'),
        ('MDF', 'MDF', 'خشب مضغوط متوسط الكثافة')
    ]
    
    for name_ar, name_fr, description in wood_types_data:
        exists = conn.execute('SELECT id FROM wood_types WHERE name_ar = ?', (name_ar,)).fetchone()
        if not exists:
            conn.execute('''
                INSERT INTO wood_types (name_ar, name_fr, description)
                VALUES (?, ?, ?)
            ''', (name_ar, name_fr, description))
    
    # إضافة منتجات تجريبية
    sample_products = [
        ('لوح خشب زان 2×4', 'PRD-BEECH-001', 1, 2.0, 4.0, 0.05, 100.0, 'قطعة', 25.0, 35.0, 20.0, 'ممتاز', 'المخزن الرئيسي - رف A1'),
        ('لوح MDF 2×3', 'PRD-MDF-001', 4, 2.0, 3.0, 0.018, 5.0, 'قطعة', 15.0, 22.0, 50.0, 'تجاري', 'المخزن الثانوي - رف C1')
    ]
    
    for product_data in sample_products:
        exists = conn.execute('SELECT id FROM products WHERE code = ?', (product_data[1],)).fetchone()
        if not exists:
            conn.execute('''
                INSERT INTO products (name, code, wood_type_id, length, width, thickness, 
                                    quantity, unit, cost_price, selling_price, min_quantity, 
                                    quality_grade, location)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', product_data)
    
    # إضافة عميل تجريبي
    customer_exists = conn.execute('SELECT id FROM customers WHERE name = ?', ('شركة الأثاث الحديث',)).fetchone()
    if not customer_exists:
        conn.execute('''
            INSERT INTO customers (name, contact_person, phone, email, address, credit_limit)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('شركة الأثاث الحديث', 'أحمد محمد', '0501234567', 
              '<EMAIL>', 'الرياض، حي الملز', 50000.0))
    
    conn.commit()
    conn.close()

# فئة المستخدم للمصادقة
class User:
    def __init__(self, id, username, email, full_name, role, is_active):
        self.id = id
        self.username = username
        self.email = email
        self.full_name = full_name
        self.role = role
        self.is_active = is_active
    
    def is_authenticated(self):
        return True
    
    def is_anonymous(self):
        return False
    
    def get_id(self):
        return str(self.id)

# الصفحة الرئيسية
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    
    # إحصائيات بسيطة
    total_products = conn.execute('SELECT COUNT(*) FROM products WHERE is_active = 1').fetchone()[0]
    active_customers = conn.execute('SELECT COUNT(*) FROM customers WHERE is_active = 1').fetchone()[0]
    
    # المنتجات التي تحتاج إعادة تموين - مع معالجة آمنة للأنواع
    try:
        low_stock_products = conn.execute('''
            SELECT * FROM products WHERE is_active = 1 LIMIT 10
        ''').fetchall()

        # فلترة المنتجات التي تحتاج إعادة تموين
        filtered_products = []
        for product in low_stock_products:
            try:
                qty = safe_float(product['quantity'])
                min_qty = safe_float(product['min_quantity'])
                if qty <= min_qty:
                    filtered_products.append(product)
                if len(filtered_products) >= 5:
                    break
            except:
                continue
        low_stock_products = filtered_products
    except Exception as e:
        print(f"خطأ في جلب المنتجات: {e}")
        low_stock_products = []
    
    conn.close()
    
    return render_template('dashboard_basic.html',
                         total_products=total_products,
                         today_sales=0,
                         active_customers=active_customers,
                         low_stock_alerts=len(low_stock_products),
                         low_stock_products=low_stock_products,
                         recent_sales=[])

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        user_data = conn.execute('''
            SELECT * FROM users WHERE username = ? AND is_active = 1
        ''', (username,)).fetchone()
        conn.close()
        
        if user_data and check_password_hash(user_data['password_hash'], password):
            session['user_id'] = user_data['id']
            session['username'] = user_data['username']
            session['full_name'] = user_data['full_name']
            session['role'] = user_data['role']
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login_basic.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

# صفحة المنتجات
@app.route('/products')
def products():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()
    try:
        products_raw = conn.execute('''
            SELECT p.*, w.name_ar as wood_type_name
            FROM products p
            LEFT JOIN wood_types w ON p.wood_type_id = w.id
            WHERE p.is_active = 1
            ORDER BY p.created_at DESC
        ''').fetchall()

        # تحويل البيانات إلى قاموس مع معالجة آمنة للأنواع
        products = []
        for product in products_raw:
            product_dict = dict(product)
            # تحويل القيم الرقمية بأمان
            product_dict['quantity'] = safe_float(product_dict.get('quantity', 0))
            product_dict['min_quantity'] = safe_float(product_dict.get('min_quantity', 0))
            product_dict['cost_price'] = safe_float(product_dict.get('cost_price', 0))
            product_dict['selling_price'] = safe_float(product_dict.get('selling_price', 0))
            product_dict['length'] = safe_float(product_dict.get('length', 0))
            product_dict['width'] = safe_float(product_dict.get('width', 0))
            product_dict['thickness'] = safe_float(product_dict.get('thickness', 0))
            products.append(product_dict)

    except Exception as e:
        print(f"خطأ في جلب المنتجات: {e}")
        products = []
    finally:
        conn.close()

    return render_template('products_basic.html', products=products)

# صفحة العملاء
@app.route('/customers')
def customers():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    conn = get_db_connection()
    try:
        customers_raw = conn.execute('''
            SELECT * FROM customers WHERE is_active = 1 ORDER BY created_at DESC
        ''').fetchall()

        # تحويل البيانات إلى قاموس مع معالجة آمنة للأنواع
        customers = []
        for customer in customers_raw:
            customer_dict = dict(customer)
            # تحويل القيم الرقمية بأمان
            customer_dict['balance'] = safe_float(customer_dict.get('balance', 0))
            customer_dict['credit_limit'] = safe_float(customer_dict.get('credit_limit', 0))
            customers.append(customer_dict)

    except Exception as e:
        print(f"خطأ في جلب العملاء: {e}")
        customers = []
    finally:
        conn.close()

    return render_template('customers_basic.html', customers=customers)

# تغيير اللغة
@app.route('/set_language/<language>')
def set_language(language=None):
    session['language'] = language
    return redirect(request.referrer or url_for('index'))

# API للحصول على معلومات النظام
@app.route('/api/system_info')
def system_info():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    
    conn = get_db_connection()
    info = {
        'total_products': conn.execute('SELECT COUNT(*) FROM products WHERE is_active = 1').fetchone()[0],
        'total_customers': conn.execute('SELECT COUNT(*) FROM customers WHERE is_active = 1').fetchone()[0],
        'total_wood_types': conn.execute('SELECT COUNT(*) FROM wood_types WHERE is_active = 1').fetchone()[0],
        'low_stock_count': conn.execute('SELECT COUNT(*) FROM products WHERE quantity <= min_quantity AND is_active = 1').fetchone()[0]
    }
    conn.close()
    
    return jsonify(info)

if __name__ == '__main__':
    # تهيئة قاعدة البيانات
    init_database()

    print("🚀 تم تشغيل نظام إدارة المخازن (النسخة المبسطة)")
    print("📍 الرابط: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("⏹️ لإيقاف النظام اضغط Ctrl+C")
    print("")
    print("ℹ️ تحذير Flask طبيعي - النظام آمن للاستخدام المحلي")
    print("   للتخلص من التحذير استخدم: run_silent.bat")
    print("   أو للإنتاج استخدم: python production_server.py")
    print("")

    # إعدادات للتطوير المحلي
    app.config['ENV'] = 'development'
    app.run(debug=True, host='0.0.0.0', port=5000)
