import os
import uuid
from datetime import datetime, date
from functools import wraps
from flask import session, redirect, url_for, flash, current_app
from flask_login import current_user
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

def generate_invoice_number(prefix='INV'):
    """توليد رقم فاتورة فريد"""
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    return f"{prefix}-{timestamp}"

def generate_product_code():
    """توليد كود منتج فريد"""
    return f"PRD-{uuid.uuid4().hex[:8].upper()}"

def format_currency(amount, currency_symbol='ر.س'):
    """تنسيق المبلغ بالعملة"""
    if amount is None:
        amount = 0
    return f"{amount:,.2f} {currency_symbol}"

def format_date(date_obj, format_str='%Y-%m-%d'):
    """تنسيق التاريخ"""
    if isinstance(date_obj, str):
        return date_obj
    if date_obj:
        return date_obj.strftime(format_str)
    return ''

def calculate_tax(amount, tax_rate):
    """حساب الضريبة"""
    if not amount or not tax_rate:
        return 0
    return (amount * tax_rate) / 100

def calculate_discount(amount, discount_rate):
    """حساب الخصم"""
    if not amount or not discount_rate:
        return 0
    return (amount * discount_rate) / 100

def require_role(role):
    """ديكوريتر للتحقق من صلاحية المستخدم"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('login'))
            if current_user.role != role and current_user.role != 'admin':
                flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
                return redirect(url_for('index'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def export_to_excel(data, filename, headers=None):
    """تصدير البيانات إلى Excel"""
    wb = openpyxl.Workbook()
    ws = wb.active
    
    # إعداد الخط والتنسيق
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # إضافة العناوين
    if headers:
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
    
    # إضافة البيانات
    for row_idx, row_data in enumerate(data, 2):
        for col_idx, value in enumerate(row_data, 1):
            ws.cell(row=row_idx, column=col_idx, value=value)
    
    # ضبط عرض الأعمدة
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # حفظ الملف
    filepath = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    wb.save(filepath)
    return filepath

def create_pdf_invoice(invoice_data, filename):
    """إنشاء فاتورة PDF"""
    filepath = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), filename)
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    doc = SimpleDocTemplate(filepath, pagesize=A4)
    story = []
    
    # الأنماط
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # وسط
    )
    
    # العنوان
    title = Paragraph(f"فاتورة رقم: {invoice_data.get('invoice_number', '')}", title_style)
    story.append(title)
    story.append(Spacer(1, 20))
    
    # معلومات الفاتورة
    info_data = [
        ['التاريخ:', invoice_data.get('invoice_date', '')],
        ['العميل:', invoice_data.get('customer_name', '')],
        ['الهاتف:', invoice_data.get('customer_phone', '')],
    ]
    
    info_table = Table(info_data, colWidths=[100, 200])
    info_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
    ]))
    
    story.append(info_table)
    story.append(Spacer(1, 30))
    
    # جدول المنتجات
    items_data = [['المنتج', 'الكمية', 'السعر', 'الإجمالي']]
    
    for item in invoice_data.get('items', []):
        items_data.append([
            item.get('product_name', ''),
            str(item.get('quantity', 0)),
            format_currency(item.get('unit_price', 0)),
            format_currency(item.get('total_price', 0))
        ])
    
    # إضافة المجاميع
    items_data.extend([
        ['', '', 'المجموع الفرعي:', format_currency(invoice_data.get('subtotal', 0))],
        ['', '', 'الضريبة:', format_currency(invoice_data.get('tax_amount', 0))],
        ['', '', 'الخصم:', format_currency(invoice_data.get('discount_amount', 0))],
        ['', '', 'الإجمالي:', format_currency(invoice_data.get('total_amount', 0))],
    ])
    
    items_table = Table(items_data, colWidths=[200, 80, 100, 100])
    items_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    
    story.append(items_table)
    
    # بناء المستند
    doc.build(story)
    return filepath

def validate_phone_number(phone):
    """التحقق من صحة رقم الهاتف"""
    import re
    # نمط بسيط للتحقق من رقم الهاتف السعودي
    pattern = r'^(05|5)[0-9]{8}$'
    return re.match(pattern, phone.replace(' ', '').replace('-', '')) is not None

def validate_email(email):
    """التحقق من صحة البريد الإلكتروني"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def get_wood_types_list():
    """قائمة أنواع الخشب المتاحة"""
    return [
        'خشب زان',
        'خشب سنديان', 
        'خشب صنوبر',
        'خشب الجوز',
        'خشب البلوط',
        'MDF',
        'خشب مضغوط',
        'خشب الأرز',
        'خشب الماهوجني',
        'أخرى'
    ]

def get_quality_grades():
    """درجات الجودة المتاحة"""
    return [
        'ممتاز',
        'جيد جداً',
        'جيد',
        'متوسط',
        'تجاري'
    ]

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    import shutil
    from datetime import datetime
    
    backup_dir = current_app.config.get('BACKUP_FOLDER', 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    
    db_path = current_app.config.get('SQLALCHEMY_DATABASE_URI').replace('sqlite:///', '')
    if os.path.exists(db_path):
        backup_filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = os.path.join(backup_dir, backup_filename)
        shutil.copy2(db_path, backup_path)
        return backup_path
    return None
