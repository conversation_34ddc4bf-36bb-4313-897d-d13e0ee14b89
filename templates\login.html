<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المخازن</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-form {
            padding: 60px 40px;
        }
        
        .login-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        
        .login-title {
            color: #333;
            font-weight: 700;
            margin-bottom: 30px;
        }
        
        .form-floating label {
            color: #666;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .language-switcher {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Language Switcher -->
    <div class="language-switcher">
        <div class="dropdown">
            <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-globe"></i>
                {% if session.language == 'fr' %}Français{% else %}العربية{% endif %}
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ url_for('set_language', language='ar') }}">العربية</a></li>
                <li><a class="dropdown-item" href="{{ url_for('set_language', language='fr') }}">Français</a></li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="login-container">
                    <div class="row g-0">
                        <!-- Login Form -->
                        <div class="col-lg-6">
                            <div class="login-form">
                                <div class="text-center mb-4">
                                    <i class="fas fa-warehouse fa-3x text-primary mb-3"></i>
                                    <h2 class="login-title">تسجيل الدخول</h2>
                                    <p class="text-muted">نظام إدارة المخازن والمحاسبة</p>
                                </div>

                                <!-- Flash Messages -->
                                {% with messages = get_flashed_messages(with_categories=true) %}
                                    {% if messages %}
                                        {% for category, message in messages %}
                                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                                {{ message }}
                                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                            </div>
                                        {% endfor %}
                                    {% endif %}
                                {% endwith %}

                                <form method="POST">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="username" name="username" placeholder="اسم المستخدم" required>
                                        <label for="username">
                                            <i class="fas fa-user me-2"></i>
                                            اسم المستخدم
                                        </label>
                                    </div>
                                    
                                    <div class="form-floating mb-4">
                                        <input type="password" class="form-control" id="password" name="password" placeholder="كلمة المرور" required>
                                        <label for="password">
                                            <i class="fas fa-lock me-2"></i>
                                            كلمة المرور
                                        </label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-login">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        دخول
                                    </button>
                                </form>
                                
                                <div class="mt-4 text-center">
                                    <small class="text-muted">
                                        المستخدم الافتراضي: admin | كلمة المرور: admin123
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Login Image -->
                        <div class="col-lg-6 d-none d-lg-block">
                            <div class="login-image">
                                <div>
                                    <i class="fas fa-warehouse fa-5x mb-4"></i>
                                    <h3 class="mb-3">نظام إدارة المخازن</h3>
                                    <p class="lead">
                                        نظام شامل لإدارة المخازن والمحاسبة<br>
                                        مع دعم كامل للغة العربية والفرنسية
                                    </p>
                                    <div class="mt-4">
                                        <i class="fas fa-check-circle me-2"></i> إدارة المخزون<br>
                                        <i class="fas fa-check-circle me-2"></i> نظام الفوترة<br>
                                        <i class="fas fa-check-circle me-2"></i> التقارير المالية<br>
                                        <i class="fas fa-check-circle me-2"></i> إدارة العملاء والموردين
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
