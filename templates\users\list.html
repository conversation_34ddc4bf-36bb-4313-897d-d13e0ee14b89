{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة المخازن{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة المستخدمين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-user-plus"></i>
                إضافة مستخدم جديد
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات المستخدمين -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ users|length }}</h4>
                        <p class="mb-0">إجمالي المستخدمين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ users|selectattr("is_active")|list|length }}</h4>
                        <p class="mb-0">مستخدمين نشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ users|selectattr("role", "equalto", "admin")|list|length }}</h4>
                        <p class="mb-0">مديرين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-shield fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ users|selectattr("role", "equalto", "user")|list|length }}</h4>
                        <p class="mb-0">موظفين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المستخدمين
        </h5>
    </div>
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم الكامل</th>
                        <th>اسم المستخدم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>
                            <div class="avatar-circle">
                                <i class="fas fa-user"></i>
                            </div>
                        </td>
                        <td>
                            <strong>{{ user.full_name }}</strong>
                        </td>
                        <td>
                            <code>{{ user.username }}</code>
                        </td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.role == 'admin' %}
                                <span class="badge bg-danger">
                                    <i class="fas fa-user-shield"></i>
                                    مدير عام
                                </span>
                            {% elif user.role == 'manager' %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-user-tie"></i>
                                    مدير
                                </span>
                            {% else %}
                                <span class="badge bg-primary">
                                    <i class="fas fa-user"></i>
                                    موظف
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check"></i>
                                    نشط
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times"></i>
                                    غير نشط
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'غير محدد' }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" title="عرض" 
                                        onclick="viewUser({{ user.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" title="تعديل"
                                        onclick="editUser({{ user.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% if user.id != current_user.id %}
                                <button type="button" class="btn btn-outline-warning" title="تغيير الحالة"
                                        onclick="toggleUserStatus({{ user.id }})">
                                    <i class="fas fa-power-off"></i>
                                </button>
                                {% if current_user.role == 'admin' %}
                                <button type="button" class="btn btn-outline-danger" title="حذف"
                                        onclick="deleteUser({{ user.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد مستخدمين</h5>
            <p class="text-muted">ابدأ بإضافة مستخدمين جدد للنظام</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-user-plus"></i>
                إضافة مستخدم جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm" method="POST" action="/users/add">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم *</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">الدور *</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">اختر الدور</option>
                                    <option value="user">موظف</option>
                                    <option value="manager">مدير</option>
                                    {% if current_user.role == 'admin' %}
                                    <option value="admin">مدير عام</option>
                                    {% endif %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                المستخدم نشط
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ المستخدم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function viewUser(userId) {
    // عرض تفاصيل المستخدم
    alert('سيتم تطوير صفحة عرض المستخدم قريباً - المستخدم رقم: ' + userId);
}

function editUser(userId) {
    // تعديل المستخدم
    alert('سيتم تطوير صفحة تعديل المستخدم قريباً - المستخدم رقم: ' + userId);
}

function toggleUserStatus(userId) {
    if (confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')) {
        // تغيير حالة المستخدم
        alert('سيتم تطوير وظيفة تغيير الحالة قريباً - المستخدم رقم: ' + userId);
    }
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // حذف المستخدم
        alert('سيتم تطوير وظيفة الحذف قريباً - المستخدم رقم: ' + userId);
    }
}

// التحقق من تطابق كلمات المرور
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password !== confirmPassword) {
        e.preventDefault();
        alert('كلمات المرور غير متطابقة');
        return false;
    }
});
</script>
{% endblock %}
