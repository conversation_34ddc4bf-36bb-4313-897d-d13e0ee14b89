<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام إدارة المخازن</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(-5px);
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.8em;
        }
        
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-warehouse"></i>
                            نظام المخازن
                        </h4>
                        <small class="text-white-50">مرحباً، {{ session.full_name or 'المستخدم' }}</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/products">
                                <i class="fas fa-boxes me-2"></i>
                                المنتجات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/customers">
                                <i class="fas fa-users me-2"></i>
                                العملاء
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-users me-2"></i>
                        إدارة العملاء
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" onclick="showAddCustomerForm()">
                                <i class="fas fa-user-plus"></i>
                                إضافة عميل جديد
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4>{{ customers|length }}</h4>
                                        <p class="mb-0">إجمالي العملاء</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="active-customers">0</h4>
                                        <p class="mb-0">عملاء نشطين</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="total-balance">0.00</h4>
                                        <p class="mb-0">إجمالي الأرصدة (ر.س)</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 id="debtors-count">0</h4>
                                        <p class="mb-0">عملاء لديهم مديونية</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            قائمة العملاء
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if customers %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم العميل</th>
                                        <th>الشخص المسؤول</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الرصيد</th>
                                        <th>حد الائتمان</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for customer in customers %}
                                    <tr>
                                        <td>
                                            <div class="avatar-circle">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        </td>
                                        <td>
                                            <strong>{{ customer.name }}</strong>
                                            {% if customer.address %}
                                            <br><small class="text-muted">{{ customer.address[:50] }}{% if customer.address|length > 50 %}...{% endif %}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {{ customer.contact_person or 'غير محدد' }}
                                        </td>
                                        <td>
                                            {% if customer.phone %}
                                                <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                                    <i class="fas fa-phone me-1"></i>
                                                    {{ customer.phone }}
                                                </a>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if customer.email %}
                                                <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                                    <i class="fas fa-envelope me-1"></i>
                                                    {{ customer.email }}
                                                </a>
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="text-primary">{{ "%.2f"|format(customer.balance) }} ر.س</span>
                                        </td>
                                        <td>
                                            {{ "%.2f"|format(customer.credit_limit) }} ر.س
                                        </td>
                                        <td>
                                            {% if customer.is_active %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check"></i>
                                                    نشط
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-pause"></i>
                                                    غير نشط
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-primary" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-success" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-info" title="الفواتير">
                                                    <i class="fas fa-file-invoice"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا يوجد عملاء</h5>
                            <p class="text-muted">ابدأ بإضافة عملاء جدد للنظام</p>
                            <button type="button" class="btn btn-primary" onclick="showAddCustomerForm()">
                                <i class="fas fa-user-plus"></i>
                                إضافة عميل جديد
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function showAddCustomerForm() {
        alert('سيتم تطوير نموذج إضافة العملاء قريباً');
    }
    
    // حساب الإحصائيات
    document.addEventListener('DOMContentLoaded', function() {
        try {
            const customers = {{ customers|tojson }};

            let activeCount = 0;
            let totalBalance = 0;
            let debtorsCount = 0;

            customers.forEach(customer => {
                try {
                    if (customer.is_active) {
                        activeCount++;
                    }
                    const balance = parseFloat(customer.balance) || 0;
                    totalBalance += balance;
                    if (balance > 0) {
                        debtorsCount++;
                    }
                } catch (e) {
                    console.log('خطأ في معالجة عميل:', e);
                }
            });

            document.getElementById('active-customers').textContent = activeCount;
            document.getElementById('total-balance').textContent = totalBalance.toFixed(2);
            document.getElementById('debtors-count').textContent = debtorsCount;
        } catch (e) {
            console.log('خطأ في حساب إحصائيات العملاء:', e);
            document.getElementById('active-customers').textContent = '0';
            document.getElementById('total-balance').textContent = '0.00';
            document.getElementById('debtors-count').textContent = '0';
        }
    });
    </script>
</body>
</html>
