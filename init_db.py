#!/usr/bin/env python3
"""
ملف تهيئة قاعدة البيانات
يقوم بإنشاء الجداول وإدخال البيانات الأساسية
"""

from app import app, db
from models import *
from werkzeug.security import generate_password_hash
from datetime import datetime, date

def init_database():
    """تهيئة قاعدة البيانات مع البيانات الأساسية"""
    
    with app.app_context():
        # إنشاء الجداول
        print("إنشاء جداول قاعدة البيانات...")
        db.create_all()
        
        # إنشاء المستخدمين الافتراضيين
        create_default_users()
        
        # إنشاء أنواع الخشب الأساسية
        create_wood_types()
        
        # إنشاء بعض المنتجات التجريبية
        create_sample_products()
        
        # إنشاء عملاء وموردين تجريبيين
        create_sample_customers_suppliers()
        
        # إنشاء إعدادات النظام الافتراضية
        create_system_settings()
        
        print("تم تهيئة قاعدة البيانات بنجاح!")

def create_default_users():
    """إنشاء المستخدمين الافتراضيين"""
    
    # التحقق من وجود المستخدم الإداري
    if not User.query.filter_by(username='admin').first():
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            full_name='المدير العام',
            role='admin',
            password_hash=generate_password_hash('admin123'),
            is_active=True
        )
        db.session.add(admin_user)
        print("تم إنشاء المستخدم الإداري: admin / admin123")
    
    # إنشاء مستخدم مدير
    if not User.query.filter_by(username='manager').first():
        manager_user = User(
            username='manager',
            email='<EMAIL>',
            full_name='مدير المخزن',
            role='manager',
            password_hash=generate_password_hash('manager123'),
            is_active=True
        )
        db.session.add(manager_user)
        print("تم إنشاء مستخدم المدير: manager / manager123")
    
    # إنشاء مستخدم عادي
    if not User.query.filter_by(username='user').first():
        normal_user = User(
            username='user',
            email='<EMAIL>',
            full_name='موظف المبيعات',
            role='user',
            password_hash=generate_password_hash('user123'),
            is_active=True
        )
        db.session.add(normal_user)
        print("تم إنشاء المستخدم العادي: user / user123")
    
    db.session.commit()

def create_wood_types():
    """إنشاء أنواع الخشب الأساسية"""
    
    wood_types_data = [
        {'name_ar': 'خشب زان', 'name_fr': 'Bois de hêtre', 'description': 'خشب صلب عالي الجودة'},
        {'name_ar': 'خشب سنديان', 'name_fr': 'Bois de chêne', 'description': 'خشب قوي ومتين'},
        {'name_ar': 'خشب صنوبر', 'name_fr': 'Bois de pin', 'description': 'خشب لين سهل التشكيل'},
        {'name_ar': 'خشب الجوز', 'name_fr': 'Bois de noyer', 'description': 'خشب فاخر بلون داكن'},
        {'name_ar': 'MDF', 'name_fr': 'MDF', 'description': 'خشب مضغوط متوسط الكثافة'},
        {'name_ar': 'خشب الأرز', 'name_fr': 'Bois de cèdre', 'description': 'خشب عطري مقاوم للحشرات'},
    ]
    
    for wood_data in wood_types_data:
        if not WoodType.query.filter_by(name_ar=wood_data['name_ar']).first():
            wood_type = WoodType(**wood_data)
            db.session.add(wood_type)
    
    db.session.commit()
    print("تم إنشاء أنواع الخشب الأساسية")

def create_sample_products():
    """إنشاء منتجات تجريبية"""
    
    # الحصول على أنواع الخشب
    wood_types = WoodType.query.all()
    
    if not wood_types:
        return
    
    sample_products = [
        {
            'name': 'لوح خشب زان 2×4',
            'code': 'PRD-BEECH-001',
            'wood_type_id': wood_types[0].id,
            'length': 2.0,
            'width': 4.0,
            'thickness': 0.05,
            'quantity': 100.0,
            'unit': 'قطعة',
            'cost_price': 25.0,
            'selling_price': 35.0,
            'min_quantity': 20.0,
            'quality_grade': 'ممتاز',
            'location': 'المخزن الرئيسي - رف A1'
        },
        {
            'name': 'لوح خشب سنديان 3×6',
            'code': 'PRD-OAK-001',
            'wood_type_id': wood_types[1].id if len(wood_types) > 1 else wood_types[0].id,
            'length': 3.0,
            'width': 6.0,
            'thickness': 0.03,
            'quantity': 75.0,
            'unit': 'قطعة',
            'cost_price': 40.0,
            'selling_price': 55.0,
            'min_quantity': 15.0,
            'quality_grade': 'جيد جداً',
            'location': 'المخزن الرئيسي - رف B1'
        },
        {
            'name': 'لوح MDF 2×3',
            'code': 'PRD-MDF-001',
            'wood_type_id': wood_types[-1].id,
            'length': 2.0,
            'width': 3.0,
            'thickness': 0.018,
            'quantity': 200.0,
            'unit': 'قطعة',
            'cost_price': 15.0,
            'selling_price': 22.0,
            'min_quantity': 50.0,
            'quality_grade': 'تجاري',
            'location': 'المخزن الثانوي - رف C1'
        }
    ]
    
    for product_data in sample_products:
        if not Product.query.filter_by(code=product_data['code']).first():
            product = Product(**product_data)
            db.session.add(product)
    
    db.session.commit()
    print("تم إنشاء المنتجات التجريبية")

def create_sample_customers_suppliers():
    """إنشاء عملاء وموردين تجريبيين"""
    
    # عملاء تجريبيين
    sample_customers = [
        {
            'name': 'شركة الأثاث الحديث',
            'contact_person': 'أحمد محمد',
            'phone': '0501234567',
            'email': '<EMAIL>',
            'address': 'الرياض، حي الملز',
            'credit_limit': 50000.0
        },
        {
            'name': 'معرض الخشب الفاخر',
            'contact_person': 'سارة أحمد',
            'phone': '0507654321',
            'email': '<EMAIL>',
            'address': 'جدة، حي الروضة',
            'credit_limit': 30000.0
        }
    ]
    
    for customer_data in sample_customers:
        if not Customer.query.filter_by(name=customer_data['name']).first():
            customer = Customer(**customer_data)
            db.session.add(customer)
    
    # موردين تجريبيين
    sample_suppliers = [
        {
            'name': 'مصنع الخشب الطبيعي',
            'contact_person': 'محمد علي',
            'phone': '0112345678',
            'email': '<EMAIL>',
            'address': 'الدمام، المنطقة الصناعية',
            'credit_limit': 100000.0
        },
        {
            'name': 'شركة الأخشاب المستوردة',
            'contact_person': 'فاطمة حسن',
            'phone': '0123456789',
            'email': '<EMAIL>',
            'address': 'الرياض، المنطقة الصناعية الثانية',
            'credit_limit': 75000.0
        }
    ]
    
    for supplier_data in sample_suppliers:
        if not Supplier.query.filter_by(name=supplier_data['name']).first():
            supplier = Supplier(**supplier_data)
            db.session.add(supplier)
    
    db.session.commit()
    print("تم إنشاء العملاء والموردين التجريبيين")

def create_system_settings():
    """إنشاء إعدادات النظام الافتراضية"""
    
    default_settings = [
        {
            'key': 'company_name',
            'value': 'شركة إدارة المخازن',
            'description': 'اسم الشركة'
        },
        {
            'key': 'company_address',
            'value': 'الرياض، المملكة العربية السعودية',
            'description': 'عنوان الشركة'
        },
        {
            'key': 'company_phone',
            'value': '011-1234567',
            'description': 'هاتف الشركة'
        },
        {
            'key': 'company_email',
            'value': '<EMAIL>',
            'description': 'بريد الشركة الإلكتروني'
        },
        {
            'key': 'default_tax_rate',
            'value': '15',
            'description': 'معدل الضريبة الافتراضي (%)'
        },
        {
            'key': 'currency_symbol',
            'value': 'ر.س',
            'description': 'رمز العملة'
        },
        {
            'key': 'low_stock_threshold',
            'value': '10',
            'description': 'حد التنبيه لنقص المخزون'
        }
    ]
    
    for setting_data in default_settings:
        if not SystemSettings.query.filter_by(key=setting_data['key']).first():
            setting = SystemSettings(**setting_data)
            db.session.add(setting)
    
    db.session.commit()
    print("تم إنشاء إعدادات النظام الافتراضية")

if __name__ == '__main__':
    init_database()
