@echo off
chcp 65001 >nul
title نظام إدارة المخازن - نسخة Babel محدثة

echo.
echo ========================================
echo    نظام إدارة المخازن والمحاسبة
echo       نسخة Babel محدثة
echo    ✅ تم حل مشكلة Babel localeselector
echo ========================================
echo.

echo 🔍 فحص متطلبات النظام...

:: فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

:: فحص وجود ملف التطبيق
if not exist "app_babel_fixed.py" (
    echo ❌ ملف التطبيق app_babel_fixed.py غير موجود
    pause
    exit /b 1
)

echo ✅ ملف التطبيق موجود

:: تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...

:: المكتبات الأساسية
echo 🔄 تثبيت Flask و SQLAlchemy...
pip install Flask==2.3.3 Flask-SQLAlchemy==3.0.5 Flask-Login==0.6.3 >nul 2>&1

:: مكتبات إضافية
echo 🔄 تثبيت مكتبات إضافية...
pip install Werkzeug==2.3.7 >nul 2>&1

:: Babel (اختياري)
echo 🔄 تثبيت Babel (اختياري)...
pip install Flask-Babel Babel >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: فشل في تثبيت Babel - سيعمل النظام بدون دعم اللغات المتعددة
) else (
    echo ✅ تم تثبيت Babel بنجاح
)

echo ✅ تم تثبيت المتطلبات الأساسية

echo.
echo 🚀 بدء تشغيل النظام (نسخة Babel محدثة)...
echo.
echo 📍 سيتم فتح النظام على: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo ✅ تم حل مشكلة AttributeError: 'Babel' object has no attribute 'localeselector'
echo ✅ النظام يعمل مع أو بدون Babel
echo 🌐 دعم اللغات المتعددة (إذا كان Babel متوفر)
echo ⏹️ لإيقاف النظام اضغط Ctrl+C
echo.

:: تشغيل النظام
python app_babel_fixed.py

echo.
echo 📴 تم إيقاف النظام
pause
