from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os
from babel import Locale
from flask_babel import Babel, gettext, ngettext, lazy_gettext

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///warehouse_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'

# إعداد دعم اللغات المتعددة
babel = Babel(app)

@babel.localeselector
def get_locale():
    # التحقق من اللغة المحفوظة في الجلسة
    if 'language' in session:
        return session['language']
    # اللغة الافتراضية
    return 'ar'

# إنشاء النماذج
from models import create_models
models_dict = create_models(db)

# استخراج النماذج
User = models_dict['User']
Product = models_dict['Product']
Customer = models_dict['Customer']
Supplier = models_dict['Supplier']
WoodType = models_dict['WoodType']
SalesInvoice = models_dict['SalesInvoice']
SalesInvoiceItem = models_dict['SalesInvoiceItem']
PurchaseInvoice = models_dict['PurchaseInvoice']
PurchaseInvoiceItem = models_dict['PurchaseInvoiceItem']
Payment = models_dict['Payment']
SystemSettings = models_dict['SystemSettings']

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
@login_required
def index():
    # إحصائيات لوحة التحكم
    from sqlalchemy import func

    # إجمالي المنتجات
    total_products = Product.query.filter_by(is_active=True).count()

    # العملاء النشطين
    active_customers = Customer.query.filter_by(is_active=True).count()

    # مبيعات اليوم
    today = datetime.utcnow().date()
    today_sales = db.session.query(func.sum(SalesInvoice.total_amount)).filter(
        SalesInvoice.invoice_date == today,
        SalesInvoice.status == 'confirmed'
    ).scalar() or 0

    # المنتجات التي تحتاج إعادة تموين
    low_stock_products = Product.query.filter(
        Product.quantity <= Product.min_quantity,
        Product.is_active == True
    ).limit(5).all()

    # آخر المبيعات
    recent_sales = SalesInvoice.query.filter_by(status='confirmed').order_by(
        SalesInvoice.created_at.desc()
    ).limit(5).all()

    return render_template('dashboard.html',
                         total_products=total_products,
                         today_sales=today_sales,
                         active_customers=active_customers,
                         low_stock_alerts=len(low_stock_products),
                         low_stock_products=low_stock_products,
                         recent_sales=recent_sales)

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

# تغيير اللغة
@app.route('/set_language/<language>')
def set_language(language=None):
    session['language'] = language
    return redirect(request.referrer or url_for('index'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name='المدير العام',
                role='admin',
                password_hash=generate_password_hash('admin123')
            )
            db.session.add(admin_user)
            db.session.commit()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
