#!/usr/bin/env python3
"""
نسخة مبسطة ومحدثة من نظام إدارة المخازن
تتجنب جميع مشاكل Jinja2 و TypeError
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import sqlite3
import os
import sys

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'warehouse-secret-key-2025-fixed'

# مسار قاعدة البيانات
DATABASE = 'warehouse_simple_fixed.db'

def safe_float(value, default=0.0):
    """تحويل آمن للقيم إلى float"""
    if value is None:
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """تحويل آمن للقيم إلى int"""
    if value is None:
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """تهيئة قاعدة البيانات"""
    conn = get_db_connection()
    
    # إنشاء جدول المستخدمين
    conn.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            full_name TEXT NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول المنتجات
    conn.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT UNIQUE NOT NULL,
            quantity REAL DEFAULT 0,
            unit TEXT DEFAULT 'قطعة',
            cost_price REAL DEFAULT 0,
            selling_price REAL DEFAULT 0,
            min_quantity REAL DEFAULT 0,
            location TEXT DEFAULT '',
            notes TEXT DEFAULT '',
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء جدول العملاء
    conn.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT DEFAULT '',
            email TEXT DEFAULT '',
            address TEXT DEFAULT '',
            balance REAL DEFAULT 0,
            is_active INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء المستخدم الافتراضي
    admin_exists = conn.execute('SELECT id FROM users WHERE username = ?', ('admin',)).fetchone()
    if not admin_exists:
        conn.execute('''
            INSERT INTO users (username, email, full_name, password_hash, role)
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', 'المدير العام', 
              generate_password_hash('admin123'), 'admin'))
    
    # إضافة منتجات تجريبية
    sample_products = [
        ('لوح خشب زان', 'PRD-001', 50.0, 'قطعة', 25.0, 35.0, 10.0, 'المخزن الرئيسي'),
        ('لوح خشب سنديان', 'PRD-002', 30.0, 'قطعة', 30.0, 45.0, 15.0, 'المخزن الرئيسي'),
        ('لوح MDF', 'PRD-003', 100.0, 'قطعة', 15.0, 22.0, 20.0, 'المخزن الثانوي')
    ]
    
    for product_data in sample_products:
        exists = conn.execute('SELECT id FROM products WHERE code = ?', (product_data[1],)).fetchone()
        if not exists:
            conn.execute('''
                INSERT INTO products (name, code, quantity, unit, cost_price, selling_price, min_quantity, location)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', product_data)
    
    # إضافة عملاء تجريبيين
    sample_customers = [
        ('شركة الأثاث الحديث', '0501234567', '<EMAIL>', 'الرياض، حي الملز', 0.0),
        ('مصنع الخشب الذهبي', '0509876543', '<EMAIL>', 'جدة، حي الفيصلية', 1500.0)
    ]
    
    for customer_data in sample_customers:
        exists = conn.execute('SELECT id FROM customers WHERE name = ?', (customer_data[0],)).fetchone()
        if not exists:
            conn.execute('''
                INSERT INTO customers (name, phone, email, address, balance)
                VALUES (?, ?, ?, ?, ?)
            ''', customer_data)
    
    conn.commit()
    conn.close()

# الصفحة الرئيسية
@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    
    try:
        # إحصائيات بسيطة
        total_products = conn.execute('SELECT COUNT(*) FROM products WHERE is_active = 1').fetchone()[0]
        active_customers = conn.execute('SELECT COUNT(*) FROM customers WHERE is_active = 1').fetchone()[0]
        
        # المنتجات التي تحتاج إعادة تموين
        products_raw = conn.execute('SELECT * FROM products WHERE is_active = 1').fetchall()
        
        low_stock_products = []
        for product in products_raw:
            qty = safe_float(product['quantity'])
            min_qty = safe_float(product['min_quantity'])
            if qty <= min_qty:
                low_stock_products.append({
                    'name': product['name'],
                    'quantity': qty,
                    'min_quantity': min_qty,
                    'unit': product['unit']
                })
        
        # حساب قيمة المخزون
        total_value = 0
        for product in products_raw:
            qty = safe_float(product['quantity'])
            price = safe_float(product['selling_price'])
            total_value += qty * price
        
    except Exception as e:
        print(f"خطأ في جلب البيانات: {e}")
        total_products = 0
        active_customers = 0
        low_stock_products = []
        total_value = 0
    finally:
        conn.close()
    
    return render_template('dashboard_simple.html',
                         total_products=total_products,
                         active_customers=active_customers,
                         low_stock_alerts=len(low_stock_products),
                         low_stock_products=low_stock_products,
                         total_value=total_value)

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        try:
            user_data = conn.execute('''
                SELECT * FROM users WHERE username = ? AND is_active = 1
            ''', (username,)).fetchone()
            
            if user_data and check_password_hash(user_data['password_hash'], password):
                session['user_id'] = user_data['id']
                session['username'] = user_data['username']
                session['full_name'] = user_data['full_name']
                session['role'] = user_data['role']
                flash('تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('index'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            flash('حدث خطأ في تسجيل الدخول', 'error')
        finally:
            conn.close()
    
    return render_template('login_simple.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

# صفحة المنتجات
@app.route('/products')
def products():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    try:
        products_raw = conn.execute('''
            SELECT * FROM products WHERE is_active = 1 ORDER BY created_at DESC
        ''').fetchall()
        
        # معالجة البيانات
        products = []
        for product in products_raw:
            qty = safe_float(product['quantity'])
            min_qty = safe_float(product['min_quantity'])
            
            # تحديد حالة المخزون
            if qty <= min_qty:
                status = 'نقص مخزون'
                status_class = 'danger'
            elif qty <= (min_qty * 1.5):
                status = 'تحذير'
                status_class = 'warning'
            else:
                status = 'متوفر'
                status_class = 'success'
            
            products.append({
                'id': product['id'],
                'name': product['name'],
                'code': product['code'],
                'quantity': qty,
                'unit': product['unit'],
                'selling_price': safe_float(product['selling_price']),
                'location': product['location'],
                'status': status,
                'status_class': status_class
            })
        
    except Exception as e:
        print(f"خطأ في جلب المنتجات: {e}")
        products = []
    finally:
        conn.close()
    
    return render_template('products_simple.html', products=products)

# صفحة العملاء
@app.route('/customers')
def customers():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    try:
        customers_raw = conn.execute('''
            SELECT * FROM customers WHERE is_active = 1 ORDER BY created_at DESC
        ''').fetchall()
        
        # معالجة البيانات
        customers = []
        for customer in customers_raw:
            balance = safe_float(customer['balance'])
            
            customers.append({
                'id': customer['id'],
                'name': customer['name'],
                'phone': customer['phone'],
                'email': customer['email'],
                'address': customer['address'],
                'balance': balance,
                'balance_formatted': f"{balance:.2f} ر.س"
            })
        
    except Exception as e:
        print(f"خطأ في جلب العملاء: {e}")
        customers = []
    finally:
        conn.close()
    
    return render_template('customers_simple.html', customers=customers)

# API للحصول على معلومات النظام
@app.route('/api/system_info')
def system_info():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    
    conn = get_db_connection()
    try:
        total_products = conn.execute('SELECT COUNT(*) FROM products WHERE is_active = 1').fetchone()[0]
        total_customers = conn.execute('SELECT COUNT(*) FROM customers WHERE is_active = 1').fetchone()[0]
        
        # حساب المنتجات التي تحتاج إعادة تموين
        products = conn.execute('SELECT quantity, min_quantity FROM products WHERE is_active = 1').fetchall()
        low_stock_count = 0
        for product in products:
            qty = safe_float(product['quantity'])
            min_qty = safe_float(product['min_quantity'])
            if qty <= min_qty:
                low_stock_count += 1
        
        info = {
            'total_products': total_products,
            'total_customers': total_customers,
            'low_stock_count': low_stock_count,
            'status': 'ممتاز - بدون أخطاء'
        }
        
    except Exception as e:
        print(f"خطأ في API: {e}")
        info = {
            'total_products': 0,
            'total_customers': 0,
            'low_stock_count': 0,
            'status': 'خطأ'
        }
    finally:
        conn.close()
    
    return jsonify(info)

if __name__ == '__main__':
    # تهيئة قاعدة البيانات
    init_database()
    
    print("🚀 تم تشغيل نظام إدارة المخازن (النسخة المبسطة المحدثة)")
    print("📍 الرابط: http://localhost:5000")
    print("👤 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("")
    print("✅ تم حل جميع مشاكل Jinja2 و TypeError!")
    print("⏹️ لإيقاف النظام اضغط Ctrl+C")
    print("")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
