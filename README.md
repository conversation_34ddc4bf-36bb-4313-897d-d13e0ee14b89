# نظام إدارة المخازن والمحاسبة
## Système de Gestion d'Entrepôt et Comptabilité

نظام شامل لإدارة المخازن والمحاسبة مصمم خصيصاً لتجار الأخشاب مع دعم كامل للغة العربية والفرنسية.

Un système complet de gestion d'entrepôt et de comptabilité conçu spécialement pour les marchands de bois avec un support complet pour l'arabe et le français.

## المميزات الرئيسية | Fonctionnalités Principales

### 🏪 إدارة المخزون | Gestion d'Inventaire
- إضافة وتعديل وحذف المنتجات
- تتبع الكميات بالمتر المكعب أو الطول/العرض
- تصنيف حسب النوع (خشب زان، سنديان، صنوبر، MDF...)
- تتبع الموردين وتواريخ التوريد
- تنبيهات لنقص المخزون

### 💰 نظام الفوترة والمحاسبة | Système de Facturation
- فواتير المبيعات والمشتريات
- فاتورة إعادة السلع
- إدارة السلع التالفة
- إمكانية التعديل على نموذج الفاتورة
- ضرائب وتخفيضات مع إمكانية التفعيل أو عدمه
- تقارير الأرباح والخسائر
- حسابات العملاء والموردين

### 💲 التسعير المتقدم | Tarification Avancée
- احتساب سعر حسب النوع والجودة والمقاسات
- تسعير حسب السوق أو الهامش الربحي
- دعم العملات المتعددة

### 👥 إدارة الطلبات والعملاء | Gestion Clients
- تتبع العملاء وتاريخ تعاملهم
- تسهيلات للدفع بالتقسيط أو المؤجل مع التنبيه لموعد الدفع

### 📊 تقارير وتحليلات | Rapports et Analyses
- تقارير دورية للمبيعات والمشتريات
- تقارير الأرباح والخسائر
- تحليلات المخزون
- إمكانية التصدير إلى Excel و PDF

### 🔐 إدارة المستخدمين | Gestion des Utilisateurs
- إنشاء حسابات المستخدمين بصلاحيات مختلفة
- أدوار متعددة: مدير عام، مدير، موظف
- نظام أمان متقدم

### 🌐 دعم اللغات | Support Multilingue
- واجهة باللغة العربية والفرنسية
- تبديل سهل بين اللغات
- تصميم يدعم الكتابة من اليمين لليسار (RTL)

## متطلبات النظام | Configuration Requise

### الحد الأدنى | Minimum
- Windows 7 أو أحدث
- Python 3.8+
- 4 GB RAM
- 1 GB مساحة تخزين

### الموصى به | Recommandé
- Windows 10/11
- Python 3.10+
- 8 GB RAM
- 5 GB مساحة تخزين
- اتصال بالإنترنت للتحديثات

## التثبيت | Installation

### الطريقة السريعة (موصى بها) | Installation Rapide
```bash
python quick_setup.py
```

### الطريقة اليدوية | Installation Manuelle

#### 1. إصلاح مشاكل التوافق | Résoudre les Problèmes de Compatibilité
```bash
python fix_dependencies.py
```

#### 2. أو التثبيت اليدوي | Installation Manuelle
```bash
pip install -r requirements.txt
```

#### 3. تشغيل النظام | Lancer le Système
```bash
python app_simple.py
```

### 4. الوصول للنظام | Accéder au Système
افتح المتصفح وانتقل إلى: `http://localhost:5000`

### حل المشاكل الشائعة | Résolution des Problèmes

#### مشكلة SQLAlchemy AssertionError:
```bash
# الحل السريع
python fix_dependencies.py

# أو الحل اليدوي
pip uninstall -y SQLAlchemy Flask-SQLAlchemy
pip install SQLAlchemy==1.4.53
pip install Flask-SQLAlchemy==3.0.5
```

## بيانات الدخول الافتراضية | Identifiants par Défaut

- **اسم المستخدم | Nom d'utilisateur:** admin
- **كلمة المرور | Mot de passe:** admin123

## الاستخدام | Utilisation

### البدء السريع | Démarrage Rapide

1. **تسجيل الدخول** باستخدام بيانات المدير الافتراضية
2. **إضافة أنواع الخشب** من قائمة إدارة المخزون
3. **إضافة المنتجات** مع تحديد الأنواع والمقاسات
4. **إضافة العملاء والموردين**
5. **إنشاء فواتير المبيعات والمشتريات**
6. **مراجعة التقارير** لمتابعة الأداء

### الوظائف الأساسية | Fonctions de Base

#### إدارة المنتجات | Gestion des Produits
- انتقل إلى "المنتجات" من القائمة الجانبية
- اضغط "إضافة منتج جديد"
- املأ البيانات المطلوبة (الاسم، الكود، النوع، المقاسات...)
- حدد الكمية والحد الأدنى للتنبيه
- احفظ المنتج

#### إنشاء فاتورة مبيعات | Créer une Facture de Vente
- انتقل إلى "فواتير المبيعات"
- اضغط "فاتورة مبيعات جديدة"
- اختر العميل
- أضف المنتجات والكميات
- راجع الحسابات والضرائب
- احفظ الفاتورة

#### عرض التقارير | Afficher les Rapports
- انتقل إلى "التقارير"
- اختر نوع التقرير والفترة الزمنية
- راجع الإحصائيات والرسوم البيانية
- صدّر التقرير إلى Excel أو PDF

## الهيكل التقني | Architecture Technique

### التقنيات المستخدمة | Technologies Utilisées
- **Backend:** Python Flask
- **Database:** SQLite
- **Frontend:** HTML5, CSS3, JavaScript
- **UI Framework:** Bootstrap 5 (RTL Support)
- **Charts:** Chart.js
- **Icons:** Font Awesome

### هيكل الملفات | Structure des Fichiers
```
warehouse_system/
├── app_simple.py          # التطبيق الرئيسي
├── models.py              # نماذج قاعدة البيانات
├── utils.py               # الوظائف المساعدة
├── translations.py        # ملف الترجمات
├── config.py              # إعدادات النظام
├── requirements.txt       # المتطلبات
├── templates/             # قوالب HTML
│   ├── base.html
│   ├── login.html
│   ├── dashboard.html
│   ├── products/
│   ├── customers/
│   ├── invoices/
│   └── reports/
└── static/               # الملفات الثابتة
    ├── css/
    ├── js/
    └── images/
```

## الأمان | Sécurité

- تشفير كلمات المرور
- جلسات آمنة
- صلاحيات متدرجة للمستخدمين
- حماية من هجمات CSRF
- تسجيل العمليات الحساسة

## النسخ الاحتياطي | Sauvegarde

يُنصح بإنشاء نسخة احتياطية من قاعدة البيانات بانتظام:
```bash
cp warehouse_system.db backup_$(date +%Y%m%d).db
```

## الدعم والمساعدة | Support et Aide

### المشاكل الشائعة | Problèmes Courants

**مشكلة:** لا يمكن الوصول للنظام
**الحل:** تأكد من تشغيل Python وأن المنفذ 5000 غير مستخدم

**مشكلة:** خطأ في قاعدة البيانات
**الحل:** احذف ملف `warehouse_system.db` وأعد تشغيل النظام

### التطوير المستقبلي | Développement Futur

- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير متقدمة بالذكاء الاصطناعي
- [ ] نظام إشعارات متقدم
- [ ] تكامل مع الماسحات الضوئية

## الترخيص | Licence

هذا النظام مطور لأغراض تجارية. جميع الحقوق محفوظة.

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2025  
**الإصدار:** 1.0.0
