#!/usr/bin/env python3
"""
إصلاح سريع لمشكلة Babel في app.py
"""

import os
import shutil
from datetime import datetime

def backup_original():
    """إنشاء نسخة احتياطية من الملف الأصلي"""
    if os.path.exists('app.py'):
        backup_name = f'app_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.py'
        shutil.copy2('app.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return True
    return False

def fix_babel_in_app_py():
    """إصلاح مشكلة Babel في app.py"""
    if not os.path.exists('app.py'):
        print("❌ ملف app.py غير موجود")
        return False
    
    try:
        # قراءة الملف الأصلي
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن المشكلة وإصلاحها
        old_pattern = "babel = Babel(app)"
        new_pattern = """babel = Babel()
babel.init_app(app)"""
        
        if old_pattern in content:
            # إصلاح المشكلة
            content = content.replace(old_pattern, new_pattern)
            
            # كتابة الملف المحدث
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إصلاح مشكلة Babel في app.py")
            return True
        else:
            print("ℹ️ لم يتم العثور على المشكلة في app.py")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إصلاح الملف: {e}")
        return False

def add_error_handling():
    """إضافة معالجة أخطاء Babel"""
    if not os.path.exists('app.py'):
        return False
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن استيراد Babel
        babel_import = "from flask_babel import Babel"
        
        if babel_import in content and "try:" not in content:
            # إضافة معالجة الأخطاء
            new_import = """# إعداد دعم اللغات المتعددة (محدث)
try:
    from flask_babel import Babel
    babel = Babel()
    babel.init_app(app)
    
    @babel.localeselector
    def get_locale():
        # التحقق من اللغة المحفوظة في الجلسة
        if 'language' in session:
            return session['language']
        # اللغة الافتراضية
        return 'ar'
    
    BABEL_AVAILABLE = True
    print("✅ Babel متوفر - دعم اللغات المتعددة مفعل")
    
except ImportError:
    BABEL_AVAILABLE = False
    print("ℹ️ Babel غير متوفر - سيتم استخدام العربية فقط")
    
    # دالة بديلة لتغيير اللغة
    def get_locale():
        return session.get('language', 'ar')"""
            
            # استبدال الكود القديم
            old_section_start = content.find("from flask_babel import Babel")
            old_section_end = content.find("return 'ar'") + len("return 'ar'")
            
            if old_section_start != -1 and old_section_end != -1:
                new_content = (content[:old_section_start] + 
                             new_import + 
                             content[old_section_end:])
                
                with open('app.py', 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ تم إضافة معالجة أخطاء Babel")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة معالجة الأخطاء: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح مشكلة Babel في app.py")
    print("=" * 40)
    
    # إنشاء نسخة احتياطية
    backup_created = backup_original()
    
    # إصلاح المشكلة
    fixed = fix_babel_in_app_py()
    
    # إضافة معالجة الأخطاء
    error_handling_added = add_error_handling()
    
    print("\n" + "=" * 40)
    print("📊 نتائج الإصلاح:")
    print("=" * 40)
    
    if backup_created:
        print("✅ تم إنشاء نسخة احتياطية")
    
    if fixed or error_handling_added:
        print("✅ تم إصلاح مشكلة Babel")
        print("\n📋 للتشغيل:")
        print("   python app.py")
        print("\n🔑 بيانات الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
    else:
        print("ℹ️ لم تكن هناك حاجة لإصلاح")
        print("\n💡 بدائل:")
        print("   python app_babel_fixed.py")
        print("   python app_simple_fixed.py")
    
    print("\n🆘 إذا استمرت المشاكل:")
    print("   pip install Flask-Babel==4.0.0")
    print("   python app_babel_fixed.py")

if __name__ == "__main__":
    main()
