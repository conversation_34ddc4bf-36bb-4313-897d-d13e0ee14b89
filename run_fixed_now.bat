@echo off
chcp 65001 >nul
title نظام إدارة المخازن - إصلاح فوري لمشكلة Babel

echo.
echo ========================================
echo    نظام إدارة المخازن والمحاسبة
echo       إصلاح فوري لمشكلة Babel
echo    ✅ حل AttributeError localeselector
echo ========================================
echo.

echo 🔍 فحص متطلبات النظام...

:: فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من https://python.org
    pause
    exit /b 1
)

echo ✅ Python متوفر

:: فحص وجود ملف التطبيق
if not exist "app_fixed_now.py" (
    echo ❌ ملف التطبيق app_fixed_now.py غير موجود
    pause
    exit /b 1
)

echo ✅ ملف التطبيق موجود

:: تثبيت المتطلبات الأساسية
echo 📦 تثبيت المتطلبات الأساسية...

echo 🔄 تثبيت Flask و SQLAlchemy...
pip install Flask==2.3.3 Flask-SQLAlchemy==3.0.5 Flask-Login==0.6.3 Werkzeug==2.3.7 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تثبيت المكتبات الأساسية
    echo 🔄 محاولة تثبيت بديل...
    pip install Flask Flask-SQLAlchemy Flask-Login Werkzeug >nul 2>&1
)

echo 🔄 تثبيت Babel (اختياري)...
pip install Flask-Babel Babel >nul 2>&1
if errorlevel 1 (
    echo ℹ️ Babel غير متوفر - سيعمل النظام بالعربية فقط
) else (
    echo ✅ تم تثبيت Babel بنجاح
)

echo ✅ تم تثبيت المتطلبات

echo.
echo 🚀 بدء تشغيل النظام (النسخة المحدثة)...
echo.
echo 📍 سيتم فتح النظام على: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo ✅ تم حل مشكلة: AttributeError: 'Babel' object has no attribute 'localeselector'
echo ✅ النظام يعمل مع أو بدون Babel
echo 🌐 دعم اللغات المتعددة (إذا كان Babel متوفر)
echo 📱 واجهة عربية كاملة
echo ⏹️ لإيقاف النظام اضغط Ctrl+C
echo.

:: تشغيل النظام
python app_fixed_now.py

echo.
echo 📴 تم إيقاف النظام
pause
